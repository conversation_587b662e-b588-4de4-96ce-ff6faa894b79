#!/usr/bin/env python3
"""
Raw exchange debugging script to see exactly what <PERSON><PERSON><PERSON> returns.
This bypasses all our custom logic and directly queries the exchange.
"""

import sys
import os
import logging
import ccxt

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.config_manager import get_exchange_credentials

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def debug_raw_exchange():
    """Debug raw exchange data."""
    
    print("=== RAW EXCHANGE DEBUGGING ===")
    
    try:
        # Get Kraken credentials
        credentials = get_exchange_credentials('kraken', 'config/settings_kraken_eur.yaml')
        
        print("\n1. INITIALIZING KRAKEN EXCHANGE:")
        print("-" * 50)
        
        print(f"Available credential keys: {list(credentials.keys())}")

        # Initialize Kraken exchange directly
        exchange = ccxt.kraken({
            'apiKey': credentials['api_key'],
            'secret': credentials['api_secret'],  # Fixed: use api_secret instead of secret
            'sandbox': credentials.get('sandbox', False),
            'enableRateLimit': True,
        })
        
        print(f"Exchange ID: {exchange.id}")
        print(f"Sandbox mode: {exchange.sandbox}")
        
        print("\n2. RAW BALANCE DATA:")
        print("-" * 50)
        
        # Fetch raw balance
        raw_balance = exchange.fetch_balance()
        
        print("Raw balance structure keys:", list(raw_balance.keys()))
        print(f"Free balances: {raw_balance.get('free', {})}")
        print(f"Used balances: {raw_balance.get('used', {})}")
        print(f"Total balances: {raw_balance.get('total', {})}")
        
        # Show non-zero free balances
        free_balances = raw_balance.get('free', {})
        non_zero_free = {k: v for k, v in free_balances.items() if v > 0}
        print(f"\nNon-zero free balances: {non_zero_free}")
        
        # Show non-zero total balances
        total_balances = raw_balance.get('total', {})
        non_zero_total = {k: v for k, v in total_balances.items() if v > 0}
        print(f"Non-zero total balances: {non_zero_total}")
        
        print("\n3. TESTING SPECIFIC SYMBOLS:")
        print("-" * 50)
        
        # Test specific symbols we expect
        test_symbols = ['SUI/EUR', 'ETH/EUR', 'CVX/EUR', 'AVAX/EUR']
        
        for symbol in test_symbols:
            try:
                print(f"\nTesting {symbol}:")
                ticker = exchange.fetch_ticker(symbol)
                print(f"  ✅ SUCCESS - Price: {ticker['last']:.8f}")
            except Exception as e:
                print(f"  ❌ FAILED - Error: {e}")
        
        print("\n4. TESTING .F SYMBOLS:")
        print("-" * 50)
        
        # Test .F symbols to see if they exist
        test_f_symbols = ['SUI.F/EUR', 'ETH.F/EUR', 'SOL.F/EUR']
        
        for symbol in test_f_symbols:
            try:
                print(f"\nTesting {symbol}:")
                ticker = exchange.fetch_ticker(symbol)
                print(f"  ✅ SUCCESS - Price: {ticker['last']:.8f}")
            except Exception as e:
                print(f"  ❌ FAILED - Error: {e}")
        
        print("\n5. MARKET SYMBOLS SEARCH:")
        print("-" * 50)
        
        # Load markets and search for SUI-related symbols
        markets = exchange.load_markets()
        
        print(f"Total markets loaded: {len(markets)}")
        
        # Search for SUI-related markets
        sui_markets = [symbol for symbol in markets.keys() if 'SUI' in symbol.upper()]
        print(f"SUI-related markets: {sui_markets}")
        
        # Search for ETH-related markets with EUR
        eth_eur_markets = [symbol for symbol in markets.keys() if 'ETH' in symbol.upper() and 'EUR' in symbol.upper()]
        print(f"ETH/EUR-related markets: {eth_eur_markets}")
        
        # Show market details for SUI/EUR if it exists
        if 'SUI/EUR' in markets:
            market = markets['SUI/EUR']
            print(f"\nSUI/EUR market details:")
            print(f"  ID: {market.get('id')}")
            print(f"  Symbol: {market.get('symbol')}")
            print(f"  Base: {market.get('base')}")
            print(f"  Quote: {market.get('quote')}")
            print(f"  Active: {market.get('active')}")
            print(f"  Type: {market.get('type')}")
        
        print("\n6. CURRENCY MAPPING:")
        print("-" * 50)
        
        # Check if there's any currency mapping happening
        currencies = exchange.currencies
        
        # Look for SUI in currencies
        sui_currencies = {k: v for k, v in currencies.items() if 'SUI' in k.upper()}
        print(f"SUI-related currencies: {sui_currencies}")
        
        # Check if SUI.F maps to something
        if 'SUI.F' in currencies:
            print(f"SUI.F currency details: {currencies['SUI.F']}")
        
        if 'SUI' in currencies:
            print(f"SUI currency details: {currencies['SUI']}")
        
        print("\n7. DIRECT BALANCE CHECK:")
        print("-" * 50)
        
        # Check if we can find SUI in any form in the balance
        all_balance_keys = set()
        for balance_type in ['free', 'used', 'total']:
            if balance_type in raw_balance:
                all_balance_keys.update(raw_balance[balance_type].keys())
        
        sui_balance_keys = [k for k in all_balance_keys if 'SUI' in k.upper()]
        print(f"All balance keys with SUI: {sui_balance_keys}")
        
        for key in sui_balance_keys:
            free_amount = raw_balance.get('free', {}).get(key, 0)
            used_amount = raw_balance.get('used', {}).get(key, 0)
            total_amount = raw_balance.get('total', {}).get(key, 0)
            print(f"  {key}: free={free_amount}, used={used_amount}, total={total_amount}")
        
    except Exception as e:
        print(f"Error in raw exchange debug: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_raw_exchange()
    print("\nRaw debugging completed!")
