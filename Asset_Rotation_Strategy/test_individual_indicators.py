#!/usr/bin/env python
"""
Individual Indicators Test Script

This script tests each fusion indicator individually to ensure they work correctly
before running the full fusion system.
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from data_fetcher import fetch_ohlcv_data
from fusion.adx_indicator import ADXIndicator
from fusion.kpss_test import KPSSTest
from fusion.adf_test import ADFTest
from fusion.pp_test import PPTest
from fusion.hurst_exponent import HurstExponent
from fusion.pmc_correlation import PMCCorrelation
from fusion.rpc_calculation import RPCCalculation
from fusion.garch_model import GARCHModel
from fusion.wavelet_transform import WaveletTransform
from fusion.halflife_calculation import HalfLifeCalculation


def get_test_data() -> pd.DataFrame:
    """Get sample BTC data for testing using existing data fetcher"""
    print("Fetching test data...")

    try:
        # Use your existing data fetcher to get BTC data from Binance
        symbols = ['BTC/USDT']
        exchange_id = 'binance'

        # Calculate date range (6 months ago)
        end_date = datetime.now()
        start_date = end_date - timedelta(days=180)

        print(f"Fetching BTC/USDT data from {start_date.date()} to {end_date.date()}")

        # Fetch data using your existing infrastructure
        data_dict = fetch_ohlcv_data(
            exchange_id=exchange_id,
            symbols=symbols,
            timeframe='1d',
            since=start_date,
            limit=200,  # Last 200 days
            use_cache=True
        )

        if 'BTC/USDT' not in data_dict or data_dict['BTC/USDT'].empty:
            raise ValueError("No BTC/USDT data retrieved")

        data = data_dict['BTC/USDT']

        print(f"Test data: {len(data)} periods from {data.index[0]} to {data.index[-1]}")
        return data

    except Exception as e:
        print(f"Error fetching data with existing fetcher: {e}")
        print("Creating synthetic test data instead...")

        # Create synthetic data as fallback
        dates = pd.date_range(start='2024-01-01', end='2024-06-30', freq='D')
        np.random.seed(42)  # For reproducible results

        # Generate realistic BTC-like price data
        initial_price = 45000
        returns = np.random.normal(0.001, 0.03, len(dates))  # Daily returns with some volatility
        prices = [initial_price]

        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))

        # Create OHLCV data
        data = pd.DataFrame({
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
            'close': prices,
            'volume': np.random.uniform(1000, 10000, len(dates))
        }, index=dates)

        # Ensure high >= close >= low and high >= open >= low
        data['high'] = data[['open', 'close', 'high']].max(axis=1)
        data['low'] = data[['open', 'close', 'low']].min(axis=1)

        print(f"Synthetic test data: {len(data)} periods from {data.index[0]} to {data.index[-1]}")
        return data


def test_indicator(indicator, name: str, data: pd.DataFrame) -> bool:
    """Test a single indicator"""
    print(f"\nTesting {name}...")
    
    try:
        # Calculate indicator
        result = indicator.calculate(data)
        
        # Basic validation
        if result.empty:
            print(f"  ❌ {name}: Empty result")
            return False
        
        valid_count = result.notna().sum()
        total_count = len(result)
        
        if valid_count == 0:
            print(f"  ❌ {name}: No valid values")
            return False
        
        # Check for reasonable values (should be normalized between -1 and 1)
        min_val = result.min()
        max_val = result.max()
        mean_val = result.mean()
        std_val = result.std()
        
        print(f"  ✅ {name}: {valid_count}/{total_count} valid values")
        print(f"     Range: [{min_val:.4f}, {max_val:.4f}]")
        print(f"     Mean: {mean_val:.4f}, Std: {std_val:.4f}")
        
        # Check if values are in expected range for normalized indicators
        if min_val < -2 or max_val > 2:
            print(f"  ⚠️  {name}: Values outside expected range [-2, 2]")
        
        # Show recent values
        recent_values = result.tail(5)
        print(f"     Recent values: {recent_values.values}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ {name}: Error - {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def run_individual_tests():
    """Run tests for all individual indicators"""
    print("=" * 60)
    print("INDIVIDUAL INDICATORS TEST")
    print("=" * 60)
    
    # Get test data
    data = get_test_data()
    
    # Initialize indicators with default parameters
    indicators = {
        "ADX": ADXIndicator(di_length=28, adx_length=20),
        "KPSS": KPSSTest(length=85),
        "ADF": ADFTest(lookback=110),
        "PP": PPTest(length=100),
        "Hurst": HurstExponent(length=25, median_length=25),
        "PMC": PMCCorrelation(correlation_length=200, momentum_type="MACD"),
        "RPC": RPCCalculation(length=100),
        "GARCH": GARCHModel(length=30),
        "Wavelet": WaveletTransform(length=50, smoothing_length=10),
        "HalfLife": HalfLifeCalculation(lookback=100)
    }
    
    # Test each indicator
    results = {}
    passed_count = 0
    
    for name, indicator in indicators.items():
        success = test_indicator(indicator, name, data)
        results[name] = success
        if success:
            passed_count += 1
    
    # Summary
    print(f"\n" + "=" * 60)
    print(f"TEST SUMMARY: {passed_count}/{len(indicators)} indicators passed")
    print("=" * 60)
    
    for name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {name}")
    
    if passed_count == len(indicators):
        print(f"\n🎉 All indicators working correctly!")
        return True
    else:
        print(f"\n⚠️  {len(indicators) - passed_count} indicators failed. Check errors above.")
        return False


def test_specific_indicator_methods():
    """Test specific methods of indicators"""
    print(f"\n" + "=" * 60)
    print("TESTING SPECIFIC INDICATOR METHODS")
    print("=" * 60)
    
    data = get_test_data()
    
    # Test ADX trend strength
    print(f"\nTesting ADX trend strength...")
    adx = ADXIndicator()
    trend_strength = adx.get_trend_strength(data)
    print(f"ADX trend strength range: [{trend_strength.min():.2f}, {trend_strength.max():.2f}]")
    
    # Test Hurst regime classification
    print(f"\nTesting Hurst regime classification...")
    hurst = HurstExponent()
    regime = hurst.get_regime_classification(data)
    regime_counts = regime.value_counts()
    print(f"Hurst regime distribution: {dict(regime_counts)}")
    
    # Test PMC correlation strength
    print(f"\nTesting PMC correlation strength...")
    pmc = PMCCorrelation()
    corr_strength = pmc.get_correlation_strength(data)
    print(f"PMC correlation range: [{corr_strength.min():.4f}, {corr_strength.max():.4f}]")
    
    # Test GARCH volatility regime
    print(f"\nTesting GARCH volatility regime...")
    garch = GARCHModel()
    vol_regime = garch.get_volatility_regime(data)
    vol_counts = vol_regime.value_counts()
    print(f"GARCH volatility regime distribution: {dict(vol_counts)}")
    
    print(f"\nSpecific method tests completed!")


if __name__ == "__main__":
    print("Starting individual indicators test...")
    
    # Run basic tests
    basic_success = run_individual_tests()
    
    if basic_success:
        # Run specific method tests
        test_specific_indicator_methods()
        
        print(f"\n🎉 All tests completed successfully!")
        print("You can now run the full fusion validation script.")
    else:
        print(f"\n❌ Some basic tests failed. Fix the issues before proceeding.")
    
    print(f"\nTest completed at {datetime.now()}")
