"""
Account management module for the Asset Rotation Strategy.
Handles account information, balance tracking, and position management.
"""

import logging
import ccxt
import time
from typing import Dict, Any, List, Optional, Tuple
import pandas as pd
from datetime import datetime

from ..config_manager import get_exchange_credentials, get_trading_config

class AccountManager:
    """
    Manages account information, balance tracking, and position management.
    """

    def __init__(self, exchange_id: str = 'binance', test_mode: bool = False, config_path: str = None):
        """
        Initialize the account manager.

        Args:
            exchange_id: The exchange ID (e.g., 'binance').
            test_mode: Whether running in test mode (skip authenticated calls).
            config_path: Path to the configuration file.
        """
        self.exchange_id = exchange_id.lower()
        self.credentials = get_exchange_credentials(exchange_id, config_path)
        self.trading_config = get_trading_config(config_path)
        self.test_mode = test_mode
        self.exchange = None
        self.positions = {}
        self.balance_history = []

        # Rate limiting for API calls
        self.last_api_call_time = 0
        self.min_api_interval = 0.1  # Minimum 100ms between API calls (10 requests per second)
        self.max_retries = 3
        self.retry_delay = 1.0  # Start with 1 second delay

        # Initialize exchange connection
        self._initialize_exchange()

    def _synchronize_time_with_server(self):
        """
        Synchronize time with server and return the time difference.

        Returns:
            Time difference in milliseconds that should be subtracted from local time
        """
        try:
            import requests

            # For Binance, get server time
            if self.exchange_id == 'binance':
                # Make multiple requests to get a more accurate measurement
                measurements = []
                for _ in range(3):
                    start_time = time.time() * 1000
                    response = requests.get('https://api.binance.com/api/v3/time', timeout=5)
                    end_time = time.time() * 1000
                    response.raise_for_status()

                    server_time = response.json()['serverTime']
                    # Account for network latency by using the midpoint
                    local_time = (start_time + end_time) / 2
                    offset = local_time - server_time
                    measurements.append(offset)

                # Use the median measurement to reduce noise
                offset = sorted(measurements)[1]

                logging.info(f"Time synchronization - Local ahead by: {offset:.0f}ms")

                # Return the offset that should be subtracted from local timestamps (as integer)
                return int(offset)
            else:
                # For other exchanges, return 0 (no offset)
                return 0

        except Exception as e:
            logging.warning(f"Failed to synchronize time with server: {e}. Using default offset of 0.")
            return 0

    def _initialize_exchange(self):
        """Initialize the exchange connection with API credentials."""
        try:
            # Get the exchange class from ccxt
            exchange_class = getattr(ccxt, self.exchange_id, None)

            if not exchange_class:
                raise ValueError(f"Exchange '{self.exchange_id}' not found in ccxt.")

            # Synchronize time with server before creating exchange instance
            time_offset = self._synchronize_time_with_server()

            # Create exchange instance with credentials
            self.exchange = exchange_class({
                'apiKey': self.credentials.get('api_key', ''),
                'secret': self.credentials.get('api_secret', ''),
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'spot',  # Use spot trading by default
                    'recvWindow': 60000,  # Increase receive window to 60 seconds for timestamp tolerance
                }
            })

            # Apply time offset by overriding the milliseconds method
            if time_offset != 0:
                original_milliseconds = self.exchange.milliseconds
                def adjusted_milliseconds():
                    return int(original_milliseconds() - time_offset)
                self.exchange.milliseconds = adjusted_milliseconds
                logging.info(f"Applied time offset of {time_offset}ms to exchange timestamps")

            # Test connection
            if not self.test_mode:
                # Load markets and test connection only if not in test mode
                self.exchange.load_markets()
                logging.info(f"Successfully loaded markets for {self.exchange_id}.")

                # Test authenticated connection only if trading is enabled
                if (self.trading_config.get('mode') == 'live' and
                    self.trading_config.get('enabled')):
                    self.exchange.fetch_balance()
                    logging.info(f"Successfully connected to {self.exchange_id} exchange.")
            else:
                logging.info(f"Test mode enabled - skipping market loading and authenticated connection test for {self.exchange_id}")

        except ccxt.InvalidNonce as e:
            # If we still get timestamp errors, try a more aggressive approach
            if "Timestamp for this request was" in str(e):
                logging.warning(f"Initial timestamp synchronization failed: {e}")
                logging.info("Attempting more aggressive time synchronization...")

                # Try with a larger safety margin
                time_offset = self._synchronize_time_with_server() + 1000  # Add 1 second safety margin

                self.exchange = exchange_class({
                    'apiKey': self.credentials.get('api_key', ''),
                    'secret': self.credentials.get('api_secret', ''),
                    'enableRateLimit': True,
                    'options': {
                        'defaultType': 'spot',
                        'recvWindow': 60000,
                    }
                })

                # Apply aggressive time offset
                if time_offset != 0:
                    original_milliseconds = self.exchange.milliseconds
                    def adjusted_milliseconds():
                        return int(original_milliseconds() - time_offset)
                    self.exchange.milliseconds = adjusted_milliseconds
                    logging.info(f"Applied aggressive time offset of {time_offset}ms to exchange timestamps")

                # Try again
                if not self.test_mode:
                    self.exchange.load_markets()
                    logging.info(f"Successfully loaded markets for {self.exchange_id} with aggressive time sync.")

                    if (self.trading_config.get('mode') == 'live' and
                        self.trading_config.get('enabled')):
                        self.exchange.fetch_balance()
                        logging.info(f"Successfully connected to {self.exchange_id} exchange with aggressive time sync.")
                else:
                    logging.info(f"Test mode enabled - skipping market loading and authenticated connection test for {self.exchange_id}")
            else:
                raise
        except Exception as e:
            logging.error(f"Error initializing exchange: {e}")
            self.exchange = None
            raise

    def _rate_limit_api_call(self):
        """Ensure we don't exceed API rate limits."""
        current_time = time.time()
        time_since_last_call = current_time - self.last_api_call_time

        if time_since_last_call < self.min_api_interval:
            sleep_time = self.min_api_interval - time_since_last_call
            logging.debug(f"Rate limiting: sleeping for {sleep_time:.3f} seconds")
            time.sleep(sleep_time)

        self.last_api_call_time = time.time()

    def _make_api_call_with_retry(self, api_func, *args, **kwargs):
        """
        Make an API call with retry logic for rate limiting errors.

        Args:
            api_func: The API function to call
            *args: Arguments for the API function
            **kwargs: Keyword arguments for the API function

        Returns:
            The result of the API call

        Raises:
            Exception: If all retries are exhausted
        """
        for attempt in range(self.max_retries):
            try:
                # Apply rate limiting
                self._rate_limit_api_call()

                # Make the API call
                result = api_func(*args, **kwargs)
                return result

            except Exception as e:
                error_str = str(e).lower()

                # Check if it's a rate limiting error
                if any(keyword in error_str for keyword in [
                    '503', 'service temporarily unavailable',
                    'rate limit', 'too many requests', '429',
                    'exceeded the limit on requests per second'
                ]):
                    if attempt < self.max_retries - 1:
                        # Exponential backoff
                        delay = self.retry_delay * (2 ** attempt)
                        logging.warning(f"Rate limit hit, retrying in {delay} seconds (attempt {attempt + 1}/{self.max_retries})")
                        time.sleep(delay)
                        continue
                    else:
                        logging.error(f"Rate limit error after {self.max_retries} attempts: {e}")
                        raise
                else:
                    # Non-rate-limiting error, don't retry
                    raise

        # This should never be reached, but just in case
        raise Exception(f"Failed to complete API call after {self.max_retries} attempts")

    def get_balance(self, currency: str = None) -> float:
        """
        Get the available balance for a specific currency with rate limiting and retry logic.

        Args:
            currency: The currency to get the balance for.

        Returns:
            The available balance for the specified currency.
        """
        # Auto-detect currency if not provided
        if currency is None:
            from src.trading.executor import EXCHANGE_QUOTE_CURRENCIES
            currency = EXCHANGE_QUOTE_CURRENCIES.get(self.exchange_id.lower(), 'USDC')
            logging.debug(f"Auto-detected quote currency: {currency} for exchange {self.exchange_id}")

        try:
            if not self.exchange:
                logging.error("Exchange not initialized.")
                return 0.0

            # Use rate-limited API call with retry logic
            balance = self._make_api_call_with_retry(self.exchange.fetch_balance)

            if currency in balance['free']:
                return float(balance['free'][currency])
            else:
                logging.warning(f"Currency {currency} not found in balance.")
                return 0.0

        except Exception as e:
            logging.error(f"Error getting balance: {e}")
            return 0.0

    def get_all_balances(self) -> Dict[str, float]:
        """
        Get all available balances with rate limiting and retry logic.

        Returns:
            A dictionary of currency -> balance.
        """
        try:
            if not self.exchange:
                logging.error("Exchange not initialized.")
                return {}

            # Use rate-limited API call with retry logic
            balance = self._make_api_call_with_retry(self.exchange.fetch_balance)

            # Filter out zero balances
            non_zero_balances = {
                currency: float(amount)
                for currency, amount in balance['free'].items()
                if float(amount) > 0
            }

            return non_zero_balances

        except Exception as e:
            logging.error(f"Error getting all balances: {e}")
            return {}

    def get_open_positions(self, rotation_assets_only: bool = True) -> Dict[str, Dict[str, Any]]:
        """
        Get all open positions.

        Args:
            rotation_assets_only: If True, only return positions for assets that are part of the rotation strategy.
                                 If False, return all positions.

        Returns:
            A dictionary of symbol -> position details.
        """
        # For spot trading, positions are just non-zero balances
        # excluding the quote currency (usually USDT)
        balances = self.get_all_balances()

        # Get the quote currency for this exchange
        from src.trading.executor import EXCHANGE_QUOTE_CURRENCIES
        exchange_quote = EXCHANGE_QUOTE_CURRENCIES.get(self.exchange_id.lower(), 'USDC')

        # Remove quote currencies
        quote_currencies = ['USDC', 'USD', 'BUSD', 'USDT', 'EUR']
        positions = {}

        # Minimum position value threshold (in EUR) to count as a real position
        min_position_value_eur = 2.0

        # Define rotation assets for filtering (if enabled)
        rotation_assets = None
        if rotation_assets_only:
            # Standard rotation assets for EUR-based exchanges
            rotation_assets = {
                'BTC/EUR', 'ETH/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR',
                'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR',
                'BNB/EUR', 'DOT/EUR'
            }

        for currency, amount in balances.items():
            if currency not in quote_currencies and amount > 0:
                # Handle Kraken's special currency suffixes (.F, .M, .S)
                # These represent staked/earning positions that should be treated as spot positions
                base_currency = currency
                if '.' in currency and currency.split('.')[-1] in ['F', 'M', 'S']:
                    base_currency = currency.split('.')[0]  # Convert SUI.F -> SUI
                    logging.debug(f"Converting {currency} to {base_currency} for spot trading")

                # For each currency, create a position entry using the base currency
                symbol = f"{base_currency}/{exchange_quote}"

                # Try to get the current market price with rate limiting
                try:
                    ticker = self._make_api_call_with_retry(self.exchange.fetch_ticker, symbol)
                    current_price = ticker['last']
                    value_usdt = amount * current_price
                except Exception as e:
                    logging.warning(f"Error getting price for {symbol}: {e}")
                    current_price = 0
                    value_usdt = 0

                # Only count as position if value is above threshold
                if value_usdt >= min_position_value_eur:
                    # Filter by rotation assets if enabled
                    if rotation_assets_only and rotation_assets and symbol not in rotation_assets:
                        logging.debug(f"Excluding non-rotation asset: {symbol} (value: €{value_usdt:.2f})")
                        continue

                    positions[symbol] = {
                        'amount': amount,
                        'current_price': current_price,
                        'value_usdt': value_usdt,
                        'entry_price': self.positions.get(symbol, {}).get('entry_price', 0),
                        'entry_time': self.positions.get(symbol, {}).get('entry_time', None),
                    }
                else:
                    logging.debug(f"Ignoring dust balance: {symbol} worth €{value_usdt:.4f} (below €{min_position_value_eur} threshold)")

        return positions

    def update_balance_history(self):
        """Update the balance history with the current balance."""
        try:
            balances = self.get_all_balances()
            positions = self.get_open_positions()

            # Get the quote currency for this exchange
            from src.trading.executor import EXCHANGE_QUOTE_CURRENCIES
            exchange_quote = EXCHANGE_QUOTE_CURRENCIES.get(self.exchange_id.lower(), 'USDC')

            # Calculate total value in quote currency
            total_value = 0

            # Add value of quote currencies
            for currency, amount in balances.items():
                if currency == exchange_quote:
                    total_value += amount

            # Add value of positions
            for symbol, position in positions.items():
                total_value += position['value_usdt']

            # Add to history
            self.balance_history.append({
                'timestamp': datetime.now(),
                f'total_value_{exchange_quote.lower()}': total_value,
                'balances': balances,
                'positions': positions
            })

            # Keep only the last 1000 entries
            if len(self.balance_history) > 1000:
                self.balance_history = self.balance_history[-1000:]

        except Exception as e:
            logging.error(f"Error updating balance history: {e}")

    def get_balance_history(self) -> pd.DataFrame:
        """
        Get the balance history as a DataFrame.

        Returns:
            A DataFrame with the balance history.
        """
        if not self.balance_history:
            return pd.DataFrame()

        # Convert to DataFrame
        df = pd.DataFrame(self.balance_history)

        # Set timestamp as index
        if 'timestamp' in df.columns:
            df.set_index('timestamp', inplace=True)

        return df

    def record_position(self, symbol: str, amount: float, price: float):
        """
        Record a new position.

        Args:
            symbol: The trading pair symbol (e.g., 'BTC/USDT').
            amount: The amount of the asset.
            price: The entry price.
        """
        self.positions[symbol] = {
            'amount': amount,
            'entry_price': price,
            'entry_time': datetime.now(),
        }

        logging.info(f"Recorded position: {symbol}, amount: {amount}, price: {price}")

        # Update balance history
        self.update_balance_history()

    def close_position(self, symbol: str):
        """
        Close a position.

        Args:
            symbol: The trading pair symbol (e.g., 'BTC/USDT').
        """
        if symbol in self.positions:
            del self.positions[symbol]
            logging.info(f"Closed position: {symbol}")

            # Update balance history
            self.update_balance_history()
        else:
            logging.warning(f"Position {symbol} not found.")
