2025-07-29 00:01:51,467 - [<PERSON>RA<PERSON><PERSON>] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-23 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 7.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 7.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 12.0, 'DOT/USDT': 9.0}
2025-07-29 00:01:51,468 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-24 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 6.0, 'SOL/USDT': 4.0, 'SUI/USDT': 0.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 5.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 12.0, 'DOT/USDT': 8.0}
2025-07-29 00:01:51,469 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-25 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 5.0, 'SOL/USDT': 4.0, 'SUI/USDT': 0.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 12.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 5.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 10.0, 'DOT/USDT': 7.0}
2025-07-29 00:01:51,470 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-26 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 5.0, 'SOL/USDT': 2.0, 'SUI/USDT': 2.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 12.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 4.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 9.0, 'DOT/USDT': 8.0}
2025-07-29 00:01:51,471 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-27 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 4.0, 'SOL/USDT': 2.0, 'SUI/USDT': 8.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 12.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 3.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 9.0, 'DOT/USDT': 7.0}
2025-07-29 00:01:51,472 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-28 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 5.0, 'SOL/USDT': 2.0, 'SUI/USDT': 8.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 12.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 6.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 10.0, 'DOT/USDT': 7.0}
2025-07-29 00:01:51,474 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-29 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 8.0, 'SOL/USDT': 2.0, 'SUI/USDT': 8.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 7.0}
2025-07-29 00:01:51,475 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-30 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 8.0, 'SOL/USDT': 2.0, 'SUI/USDT': 8.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 12.0, 'DOT/USDT': 7.0}
2025-07-29 00:01:51,476 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-31 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 8.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 11.0, 'DOT/USDT': 6.0}
2025-07-29 00:01:51,477 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-01 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 8.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 11.0, 'DOT/USDT': 6.0}
2025-07-29 00:01:51,478 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-02 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 2.0, 'SUI/USDT': 9.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 8.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 6.0}
2025-07-29 00:01:51,479 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-03 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 10.0, 'SOL/USDT': 1.0, 'SUI/USDT': 9.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 7.0}
2025-07-29 00:01:51,480 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-04 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 1.0, 'SUI/USDT': 8.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 1.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 11.0, 'DOT/USDT': 6.0}
2025-07-29 00:01:51,481 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-05 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 1.0, 'SUI/USDT': 8.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 1.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 11.0, 'DOT/USDT': 6.0}
2025-07-29 00:01:51,482 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-06 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 1.0, 'SUI/USDT': 5.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 1.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 7.0, 'BNB/USDT': 11.0, 'DOT/USDT': 8.0}
2025-07-29 00:01:51,483 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-07 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 2.0, 'SUI/USDT': 5.0, 'XRP/USDT': 5.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 1.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 7.0, 'BNB/USDT': 10.0, 'DOT/USDT': 7.0}
2025-07-29 00:01:51,484 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-08 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 11.0, 'SOL/USDT': 4.0, 'SUI/USDT': 5.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 1.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 11.0, 'DOT/USDT': 5.0}
2025-07-29 00:01:51,485 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-09 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 11.0, 'SOL/USDT': 5.0, 'SUI/USDT': 6.0, 'XRP/USDT': 5.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 8.0, 'ADA/USDT': 1.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 11.0, 'DOT/USDT': 2.0}
2025-07-29 00:01:51,486 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-10 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 11.0, 'SOL/USDT': 5.0, 'SUI/USDT': 6.0, 'XRP/USDT': 5.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 11.0, 'DOT/USDT': 2.0}
2025-07-29 00:01:51,487 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-11 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 11.0, 'SOL/USDT': 6.0, 'SUI/USDT': 6.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 10.0, 'DOT/USDT': 2.0}
2025-07-29 00:01:51,488 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-12 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 11.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 7.0, 'DOT/USDT': 2.0}
2025-07-29 00:01:51,489 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-13 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 10.0, 'SOL/USDT': 8.0, 'SUI/USDT': 5.0, 'XRP/USDT': 5.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 11.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 7.0, 'DOT/USDT': 2.0}
2025-07-29 00:01:51,490 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-14 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 8.0, 'SUI/USDT': 5.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 11.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 7.0, 'DOT/USDT': 2.0}
2025-07-29 00:01:51,491 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-15 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 8.0, 'SUI/USDT': 5.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 11.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-29 00:01:51,493 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-16 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 11.0, 'SUI/USDT': 4.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-29 00:01:51,494 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-17 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 12.0, 'SUI/USDT': 4.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-29 00:01:51,495 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-18 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 12.0, 'SUI/USDT': 4.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-29 00:01:51,496 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-19 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 13.0, 'SUI/USDT': 4.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 2.0, 'LINK/USDT': 5.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-29 00:01:51,497 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-20 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 13.0, 'SUI/USDT': 3.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 2.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 3.0}
2025-07-29 00:01:51,498 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-21 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 10.0, 'SOL/USDT': 13.0, 'SUI/USDT': 3.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 2.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-29 00:01:51,499 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-22 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 9.0, 'SOL/USDT': 12.0, 'SUI/USDT': 9.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 2.0, 'LINK/USDT': 5.0, 'TRX/USDT': 6.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 5.0, 'DOT/USDT': 1.0}
2025-07-29 00:01:51,499 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-04-23:
2025-07-29 00:01:51,499 - [KRAKEN] - root - INFO -    Signal Date: 2025-04-22 (generated at 00:00 UTC)
2025-07-29 00:01:51,500 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-04-23 00:00 UTC (immediate)
2025-07-29 00:01:51,500 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-29 00:01:51,500 - [KRAKEN] - root - INFO -    Buying: ['SOL/USDT', 'PEPE/USDT']
2025-07-29 00:01:51,500 - [KRAKEN] - root - INFO -    SOL/USDT buy price: $151.1000 (close price)
2025-07-29 00:01:51,500 - [KRAKEN] - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-07-29 00:01:51,501 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-23 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 8.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 2.0, 'LINK/USDT': 8.0, 'TRX/USDT': 4.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 1.0}
2025-07-29 00:01:51,501 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-04-24:
2025-07-29 00:01:51,501 - [KRAKEN] - root - INFO -    Signal Date: 2025-04-23 (generated at 00:00 UTC)
2025-07-29 00:01:51,501 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-04-24 00:00 UTC (immediate)
2025-07-29 00:01:51,501 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-29 00:01:51,501 - [KRAKEN] - root - INFO -    Selling: ['PEPE/USDT']
2025-07-29 00:01:51,502 - [KRAKEN] - root - INFO -    Buying: ['SUI/USDT']
2025-07-29 00:01:51,502 - [KRAKEN] - root - INFO -    SUI/USDT buy price: $3.3437 (close price)
2025-07-29 00:01:51,503 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-24 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 8.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 4.0, 'LINK/USDT': 8.0, 'TRX/USDT': 3.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-29 00:01:51,504 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-25 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 8.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 4.0, 'LINK/USDT': 8.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-29 00:01:51,504 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-04-26:
2025-07-29 00:01:51,504 - [KRAKEN] - root - INFO -    Signal Date: 2025-04-25 (generated at 00:00 UTC)
2025-07-29 00:01:51,504 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-04-26 00:00 UTC (immediate)
2025-07-29 00:01:51,504 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-29 00:01:51,504 - [KRAKEN] - root - INFO -    Selling: ['SOL/USDT']
2025-07-29 00:01:51,505 - [KRAKEN] - root - INFO -    Buying: ['PEPE/USDT']
2025-07-29 00:01:51,505 - [KRAKEN] - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-07-29 00:01:51,506 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-26 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 4.0, 'LINK/USDT': 7.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-29 00:01:51,507 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-27 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-29 00:01:51,508 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-28 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 5.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-29 00:01:51,510 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-29 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 7.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 5.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-29 00:01:51,511 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-30 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 7.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 5.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-29 00:01:51,512 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-01 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-29 00:01:51,513 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-02 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 7.0, 'SOL/USDT': 10.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-29 00:01:51,515 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-03 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 8.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-29 00:01:51,515 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-05-04:
2025-07-29 00:01:51,515 - [KRAKEN] - root - INFO -    Signal Date: 2025-05-03 (generated at 00:00 UTC)
2025-07-29 00:01:51,515 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-05-04 00:00 UTC (immediate)
2025-07-29 00:01:51,515 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-29 00:01:51,515 - [KRAKEN] - root - INFO -    Selling: ['PEPE/USDT']
2025-07-29 00:01:51,515 - [KRAKEN] - root - INFO -    Buying: ['AAVE/USDT']
2025-07-29 00:01:51,515 - [KRAKEN] - root - INFO -    AAVE/USDT buy price: $171.1100 (close price)
2025-07-29 00:01:51,517 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-04 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 9.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-29 00:01:51,518 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-05 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 9.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-29 00:01:51,519 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-06 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 10.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-29 00:01:51,520 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-07 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 10.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 0.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-29 00:01:51,522 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-08 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 6.0, 'SOL/USDT': 9.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-29 00:01:51,522 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-05-09:
2025-07-29 00:01:51,522 - [KRAKEN] - root - INFO -    Signal Date: 2025-05-08 (generated at 00:00 UTC)
2025-07-29 00:01:51,522 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-05-09 00:00 UTC (immediate)
2025-07-29 00:01:51,522 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-29 00:01:51,522 - [KRAKEN] - root - INFO -    Selling: ['AAVE/USDT']
2025-07-29 00:01:51,522 - [KRAKEN] - root - INFO -    Buying: ['PEPE/USDT']
2025-07-29 00:01:51,522 - [KRAKEN] - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-07-29 00:01:51,524 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-09 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 4.0, 'SOL/USDT': 9.0, 'SUI/USDT': 12.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 8.0, 'BNB/USDT': 1.0, 'DOT/USDT': 7.0}
2025-07-29 00:01:51,525 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-10 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 3.0, 'SOL/USDT': 7.0, 'SUI/USDT': 12.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 6.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 8.0}
2025-07-29 00:01:51,526 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-11 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 3.0, 'SOL/USDT': 7.0, 'SUI/USDT': 12.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 6.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 8.0}
2025-07-29 00:01:51,527 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-12 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 2.0, 'SOL/USDT': 7.0, 'SUI/USDT': 11.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 6.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 8.0}
2025-07-29 00:01:51,528 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-05-13:
2025-07-29 00:01:51,528 - [KRAKEN] - root - INFO -    Signal Date: 2025-05-12 (generated at 00:00 UTC)
2025-07-29 00:01:51,528 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-05-13 00:00 UTC (immediate)
2025-07-29 00:01:51,528 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-29 00:01:51,528 - [KRAKEN] - root - INFO -    Selling: ['SUI/USDT']
2025-07-29 00:01:51,528 - [KRAKEN] - root - INFO -    Buying: ['ETH/USDT']
2025-07-29 00:01:51,528 - [KRAKEN] - root - INFO -    ETH/USDT buy price: $2679.7100 (close price)
2025-07-29 00:01:51,529 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-13 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 2.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 6.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 8.0}
2025-07-29 00:01:51,530 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-14 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 2.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-29 00:01:51,532 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-15 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 2.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-29 00:01:51,533 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-16 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 2.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-29 00:01:51,534 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-17 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 4.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-29 00:01:51,535 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-18 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 4.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-29 00:01:51,537 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-19 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 5.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-29 00:01:51,537 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-05-20:
2025-07-29 00:01:51,537 - [KRAKEN] - root - INFO -    Signal Date: 2025-05-19 (generated at 00:00 UTC)
2025-07-29 00:01:51,537 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-05-20 00:00 UTC (immediate)
2025-07-29 00:01:51,537 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-29 00:01:51,537 - [KRAKEN] - root - INFO -    Selling: ['ETH/USDT']
2025-07-29 00:01:51,537 - [KRAKEN] - root - INFO -    Buying: ['AAVE/USDT']
2025-07-29 00:01:51,537 - [KRAKEN] - root - INFO -    AAVE/USDT buy price: $259.4600 (close price)
2025-07-29 00:01:51,539 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-20 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 6.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-29 00:01:51,540 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-21 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 6.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 6.0}
2025-07-29 00:01:51,540 - [KRAKEN] - root - INFO - [TIE-BREAKING] Incumbent approach (weighted): Keeping incumbents ['PEPE/USDT', 'AAVE/USDT'] from tied assets ['AAVE/USDT', 'PEPE/USDT']
2025-07-29 00:01:51,541 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-22 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 6.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 1.0, 'DOT/USDT': 6.0}
2025-07-29 00:01:51,542 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-23 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 7.0, 'SOL/USDT': 9.0, 'SUI/USDT': 7.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 2.0, 'DOT/USDT': 6.0}
2025-07-29 00:01:51,544 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-24 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 7.0, 'SOL/USDT': 9.0, 'SUI/USDT': 6.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 2.0, 'LINK/USDT': 1.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 4.0, 'DOT/USDT': 5.0}
2025-07-29 00:01:51,545 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-25 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 8.0, 'SOL/USDT': 9.0, 'SUI/USDT': 3.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 2.0, 'LINK/USDT': 1.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 5.0, 'DOT/USDT': 2.0}
2025-07-29 00:01:51,546 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-26 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 8.0, 'SOL/USDT': 9.0, 'SUI/USDT': 0.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 2.0, 'TRX/USDT': 4.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 6.0, 'DOT/USDT': 1.0}
2025-07-29 00:01:51,547 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-27 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 8.0, 'SOL/USDT': 9.0, 'SUI/USDT': 0.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 2.0, 'TRX/USDT': 5.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 7.0, 'DOT/USDT': 1.0}
2025-07-29 00:01:51,549 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-28 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 7.0, 'SOL/USDT': 8.0, 'SUI/USDT': 0.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 2.0, 'LINK/USDT': 2.0, 'TRX/USDT': 5.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 8.0, 'DOT/USDT': 1.0}
2025-07-29 00:01:51,550 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-29 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 6.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 2.0, 'LINK/USDT': 2.0, 'TRX/USDT': 6.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-29 00:01:51,551 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-30 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 10.0, 'DOT/USDT': 0.0}
2025-07-29 00:01:51,551 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-05-31:
2025-07-29 00:01:51,551 - [KRAKEN] - root - INFO -    Signal Date: 2025-05-30 (generated at 00:00 UTC)
2025-07-29 00:01:51,551 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-05-31 00:00 UTC (immediate)
2025-07-29 00:01:51,551 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-29 00:01:51,552 - [KRAKEN] - root - INFO -    Selling: ['PEPE/USDT']
2025-07-29 00:01:51,552 - [KRAKEN] - root - INFO -    Buying: ['ETH/USDT']
2025-07-29 00:01:51,552 - [KRAKEN] - root - INFO -    ETH/USDT buy price: $2528.0600 (close price)
2025-07-29 00:01:51,553 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-31 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 10.0, 'DOT/USDT': 0.0}
2025-07-29 00:01:51,554 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-01 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 10.0, 'DOT/USDT': 0.0}
2025-07-29 00:01:51,555 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-02 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 10.0, 'DOT/USDT': 0.0}
2025-07-29 00:01:51,557 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-03 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 10.0, 'DOT/USDT': 0.0}
2025-07-29 00:01:51,558 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-04 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 9.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 9.0, 'DOT/USDT': 0.0}
2025-07-29 00:01:51,559 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-05 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 9.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 2.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 2.0}
2025-07-29 00:01:51,559 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-06-06:
2025-07-29 00:01:51,559 - [KRAKEN] - root - INFO -    Signal Date: 2025-06-05 (generated at 00:00 UTC)
2025-07-29 00:01:51,560 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-06-06 00:00 UTC (immediate)
2025-07-29 00:01:51,560 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-29 00:01:51,560 - [KRAKEN] - root - INFO -    Selling: ['ETH/USDT']
2025-07-29 00:01:51,560 - [KRAKEN] - root - INFO -    Buying: ['TRX/USDT']
2025-07-29 00:01:51,560 - [KRAKEN] - root - INFO -    TRX/USDT buy price: $0.2779 (close price)
2025-07-29 00:01:51,561 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-06 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 9.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 4.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 2.0}
2025-07-29 00:01:51,562 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-07 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 9.0, 'SOL/USDT': 5.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 4.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 2.0}
2025-07-29 00:01:51,563 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-08 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 9.0, 'SOL/USDT': 5.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 4.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 2.0}
2025-07-29 00:01:51,565 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-09 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 5.0, 'SUI/USDT': 1.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 4.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-29 00:01:51,566 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-10 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-29 00:01:51,566 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-06-11:
2025-07-29 00:01:51,566 - [KRAKEN] - root - INFO -    Signal Date: 2025-06-10 (generated at 00:00 UTC)
2025-07-29 00:01:51,566 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-06-11 00:00 UTC (immediate)
2025-07-29 00:01:51,566 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-29 00:01:51,566 - [KRAKEN] - root - INFO -    Selling: ['TRX/USDT']
2025-07-29 00:01:51,567 - [KRAKEN] - root - INFO -    Buying: ['ETH/USDT']
2025-07-29 00:01:51,567 - [KRAKEN] - root - INFO -    ETH/USDT buy price: $2771.6100 (close price)
2025-07-29 00:01:51,568 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-11 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-29 00:01:51,569 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-12 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-29 00:01:51,570 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-13 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 5.0, 'SUI/USDT': 1.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 5.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-29 00:01:51,572 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-14 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 5.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 5.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-29 00:01:51,573 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-15 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 5.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-29 00:01:51,574 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-16 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 3.0}
2025-07-29 00:01:51,576 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-17 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 4.0}
2025-07-29 00:01:51,577 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-18 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 4.0}
2025-07-29 00:01:51,578 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-19 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 3.0}
2025-07-29 00:01:51,579 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-06-20:
2025-07-29 00:01:51,579 - [KRAKEN] - root - INFO -    Signal Date: 2025-06-19 (generated at 00:00 UTC)
2025-07-29 00:01:51,579 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-06-20 00:00 UTC (immediate)
2025-07-29 00:01:51,579 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-29 00:01:51,579 - [KRAKEN] - root - INFO -    Selling: ['ETH/USDT']
2025-07-29 00:01:51,579 - [KRAKEN] - root - INFO -    Buying: ['TRX/USDT']
2025-07-29 00:01:51,579 - [KRAKEN] - root - INFO -    TRX/USDT buy price: $0.2722 (close price)
2025-07-29 00:01:51,580 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-20 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 10.0, 'DOT/USDT': 3.0}
2025-07-29 00:01:51,580 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-06-21:
2025-07-29 00:01:51,581 - [KRAKEN] - root - INFO -    Signal Date: 2025-06-20 (generated at 00:00 UTC)
2025-07-29 00:01:51,581 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-06-21 00:00 UTC (immediate)
2025-07-29 00:01:51,581 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-29 00:01:51,581 - [KRAKEN] - root - INFO -    Selling: ['TRX/USDT', 'AAVE/USDT']
2025-07-29 00:01:51,582 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-21 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 11.0, 'DOT/USDT': 4.0}
2025-07-29 00:01:51,583 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-22 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 11.0, 'DOT/USDT': 2.0}
2025-07-29 00:01:51,584 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-23 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 7.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-29 00:01:51,585 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-24 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-29 00:01:51,586 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-25 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-29 00:01:51,587 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-26 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 13.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-29 00:01:51,589 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-27 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 13.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 2.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-29 00:01:51,590 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-28 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 13.0, 'SOL/USDT': 7.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-29 00:01:51,591 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-29 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 12.0, 'SOL/USDT': 7.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 10.0, 'DOT/USDT': 2.0}
2025-07-29 00:01:51,592 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-30 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 12.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-29 00:01:51,593 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-01 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-29 00:01:51,594 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-02 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-29 00:01:51,595 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-03 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 1.0}
2025-07-29 00:01:51,597 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-04 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 1.0}
2025-07-29 00:01:51,598 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-05 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 1.0}
2025-07-29 00:01:51,599 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-06 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 0.0}
2025-07-29 00:01:51,600 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-07 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 0.0}
2025-07-29 00:01:51,601 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-08 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 5.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 7.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 6.0, 'DOT/USDT': 0.0}
2025-07-29 00:01:51,602 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-09 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 5.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 1.0, 'LINK/USDT': 7.0, 'TRX/USDT': 9.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 4.0, 'DOT/USDT': 0.0}
2025-07-29 00:01:51,604 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-10 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 3.0, 'SOL/USDT': 4.0, 'SUI/USDT': 10.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 3.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 0.0}
2025-07-29 00:01:51,604 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-07-11:
2025-07-29 00:01:51,604 - [KRAKEN] - root - INFO -    Signal Date: 2025-07-10 (generated at 00:00 UTC)
2025-07-29 00:01:51,604 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-07-11 00:00 UTC (immediate)
2025-07-29 00:01:51,604 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-29 00:01:51,604 - [KRAKEN] - root - INFO -    Buying: ['XRP/USDT', 'SUI/USDT']
2025-07-29 00:01:51,604 - [KRAKEN] - root - INFO -    XRP/USDT buy price: $2.7322 (close price)
2025-07-29 00:01:51,604 - [KRAKEN] - root - INFO -    SUI/USDT buy price: $3.3868 (close price)
2025-07-29 00:01:51,605 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-11 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 3.0, 'SOL/USDT': 3.0, 'SUI/USDT': 10.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 8.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 0.0}
2025-07-29 00:01:51,606 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-07-12:
2025-07-29 00:01:51,606 - [KRAKEN] - root - INFO -    Signal Date: 2025-07-11 (generated at 00:00 UTC)
2025-07-29 00:01:51,606 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-07-12 00:00 UTC (immediate)
2025-07-29 00:01:51,606 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-29 00:01:51,606 - [KRAKEN] - root - INFO -    Selling: ['SUI/USDT']
2025-07-29 00:01:51,606 - [KRAKEN] - root - INFO -    Buying: ['ETH/USDT']
2025-07-29 00:01:51,606 - [KRAKEN] - root - INFO -    ETH/USDT buy price: $2943.2800 (close price)
2025-07-29 00:01:51,607 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-12 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 3.0, 'SOL/USDT': 3.0, 'SUI/USDT': 10.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 9.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 8.0, 'BNB/USDT': 0.0, 'DOT/USDT': 0.0}
2025-07-29 00:01:51,608 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-07-13:
2025-07-29 00:01:51,608 - [KRAKEN] - root - INFO -    Signal Date: 2025-07-12 (generated at 00:00 UTC)
2025-07-29 00:01:51,608 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-07-13 00:00 UTC (immediate)
2025-07-29 00:01:51,608 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-29 00:01:51,608 - [KRAKEN] - root - INFO -    Selling: ['ETH/USDT']
2025-07-29 00:01:51,608 - [KRAKEN] - root - INFO -    Buying: ['SUI/USDT']
2025-07-29 00:01:51,608 - [KRAKEN] - root - INFO -    SUI/USDT buy price: $3.4899 (close price)
2025-07-29 00:01:51,610 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-13 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 4.0, 'SOL/USDT': 2.0, 'SUI/USDT': 9.0, 'XRP/USDT': 13.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 10.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 8.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-29 00:01:51,610 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-07-14:
2025-07-29 00:01:51,610 - [KRAKEN] - root - INFO -    Signal Date: 2025-07-13 (generated at 00:00 UTC)
2025-07-29 00:01:51,610 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-07-14 00:00 UTC (immediate)
2025-07-29 00:01:51,611 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-29 00:01:51,611 - [KRAKEN] - root - INFO -    Selling: ['SUI/USDT']
2025-07-29 00:01:51,611 - [KRAKEN] - root - INFO -    Buying: ['ADA/USDT']
2025-07-29 00:01:51,611 - [KRAKEN] - root - INFO -    ADA/USDT buy price: $0.7352 (close price)
2025-07-29 00:01:51,612 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-14 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 4.0, 'SOL/USDT': 2.0, 'SUI/USDT': 12.0, 'XRP/USDT': 13.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 10.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 7.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-29 00:01:51,612 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-07-15:
2025-07-29 00:01:51,612 - [KRAKEN] - root - INFO -    Signal Date: 2025-07-14 (generated at 00:00 UTC)
2025-07-29 00:01:51,612 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-07-15 00:00 UTC (immediate)
2025-07-29 00:01:51,612 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-29 00:01:51,612 - [KRAKEN] - root - INFO -    Selling: ['ADA/USDT']
2025-07-29 00:01:51,613 - [KRAKEN] - root - INFO -    Buying: ['SUI/USDT']
2025-07-29 00:01:51,613 - [KRAKEN] - root - INFO -    SUI/USDT buy price: $4.1014 (close price)
2025-07-29 00:01:51,614 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-15 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 3.0, 'SOL/USDT': 2.0, 'SUI/USDT': 13.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 10.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-29 00:01:51,615 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-16 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 2.0, 'SOL/USDT': 3.0, 'SUI/USDT': 13.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 9.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-29 00:01:51,617 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-17 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 1.0, 'SOL/USDT': 3.0, 'SUI/USDT': 12.0, 'XRP/USDT': 13.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 10.0, 'LINK/USDT': 8.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-29 00:01:51,618 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-18 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 0.0, 'SOL/USDT': 3.0, 'SUI/USDT': 8.0, 'XRP/USDT': 13.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 10.0, 'LINK/USDT': 8.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 1.0, 'DOT/USDT': 4.0}
2025-07-29 00:01:51,618 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-07-19:
2025-07-29 00:01:51,619 - [KRAKEN] - root - INFO -    Signal Date: 2025-07-18 (generated at 00:00 UTC)
2025-07-29 00:01:51,619 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-07-19 00:00 UTC (immediate)
2025-07-29 00:01:51,619 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-29 00:01:51,619 - [KRAKEN] - root - INFO -    Selling: ['SUI/USDT']
2025-07-29 00:01:51,619 - [KRAKEN] - root - INFO -    Buying: ['ETH/USDT']
2025-07-29 00:01:51,619 - [KRAKEN] - root - INFO -    ETH/USDT buy price: $3592.0100 (close price)
2025-07-29 00:01:51,620 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-19 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 0.0, 'SOL/USDT': 3.0, 'SUI/USDT': 7.0, 'XRP/USDT': 13.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 10.0, 'LINK/USDT': 9.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 11.0, 'BNB/USDT': 1.0, 'DOT/USDT': 5.0}
2025-07-29 00:01:51,622 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-20 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 0.0, 'SOL/USDT': 3.0, 'SUI/USDT': 6.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 3.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 10.0, 'LINK/USDT': 9.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 13.0, 'BNB/USDT': 2.0, 'DOT/USDT': 5.0}
2025-07-29 00:01:51,622 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-07-21:
2025-07-29 00:01:51,622 - [KRAKEN] - root - INFO -    Signal Date: 2025-07-20 (generated at 00:00 UTC)
2025-07-29 00:01:51,622 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-07-21 00:00 UTC (immediate)
2025-07-29 00:01:51,622 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-29 00:01:51,622 - [KRAKEN] - root - INFO -    Selling: ['ETH/USDT']
2025-07-29 00:01:51,622 - [KRAKEN] - root - INFO -    Buying: ['DOGE/USDT']
2025-07-29 00:01:51,622 - [KRAKEN] - root - INFO -    DOGE/USDT buy price: $0.2714 (close price)
2025-07-29 00:01:51,624 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-21 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 0.0, 'SOL/USDT': 5.0, 'SUI/USDT': 6.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 3.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 10.0, 'LINK/USDT': 9.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 13.0, 'BNB/USDT': 2.0, 'DOT/USDT': 4.0}
2025-07-29 00:01:51,625 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-22 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 0.0, 'SOL/USDT': 5.0, 'SUI/USDT': 5.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 11.0, 'LINK/USDT': 9.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 13.0, 'BNB/USDT': 3.0, 'DOT/USDT': 4.0}
2025-07-29 00:01:51,627 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-23 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 0.0, 'SOL/USDT': 5.0, 'SUI/USDT': 5.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 10.0, 'LINK/USDT': 9.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 13.0, 'BNB/USDT': 3.0, 'DOT/USDT': 3.0}
2025-07-29 00:01:51,627 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-07-24:
2025-07-29 00:01:51,627 - [KRAKEN] - root - INFO -    Signal Date: 2025-07-23 (generated at 00:00 UTC)
2025-07-29 00:01:51,627 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-07-24 00:00 UTC (immediate)
2025-07-29 00:01:51,627 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-29 00:01:51,627 - [KRAKEN] - root - INFO -    Selling: ['XRP/USDT']
2025-07-29 00:01:51,627 - [KRAKEN] - root - INFO -    Buying: ['ETH/USDT']
2025-07-29 00:01:51,627 - [KRAKEN] - root - INFO -    ETH/USDT buy price: $3706.9400 (close price)
2025-07-29 00:01:51,629 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-24 00:00:00+00:00: {'ETH/USDT': 13.0, 'BTC/USDT': 1.0, 'SOL/USDT': 5.0, 'SUI/USDT': 6.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 10.0, 'LINK/USDT': 9.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 12.0, 'BNB/USDT': 4.0, 'DOT/USDT': 3.0}
2025-07-29 00:01:51,630 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-25 00:00:00+00:00: {'ETH/USDT': 13.0, 'BTC/USDT': 1.0, 'SOL/USDT': 5.0, 'SUI/USDT': 7.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 10.0, 'LINK/USDT': 9.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 12.0, 'BNB/USDT': 4.0, 'DOT/USDT': 3.0}
2025-07-29 00:01:51,632 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-26 00:00:00+00:00: {'ETH/USDT': 13.0, 'BTC/USDT': 1.0, 'SOL/USDT': 5.0, 'SUI/USDT': 12.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 9.0, 'LINK/USDT': 8.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 4.0, 'DOGE/USDT': 11.0, 'BNB/USDT': 4.0, 'DOT/USDT': 3.0}
2025-07-29 00:01:51,632 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-07-27:
2025-07-29 00:01:51,632 - [KRAKEN] - root - INFO -    Signal Date: 2025-07-26 (generated at 00:00 UTC)
2025-07-29 00:01:51,632 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-07-27 00:00 UTC (immediate)
2025-07-29 00:01:51,632 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-29 00:01:51,632 - [KRAKEN] - root - INFO -    Selling: ['DOGE/USDT']
2025-07-29 00:01:51,632 - [KRAKEN] - root - INFO -    Buying: ['SUI/USDT']
2025-07-29 00:01:51,632 - [KRAKEN] - root - INFO -    SUI/USDT buy price: $4.3260 (close price)
2025-07-29 00:01:51,634 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-27 00:00:00+00:00: {'ETH/USDT': 13.0, 'BTC/USDT': 1.0, 'SOL/USDT': 4.0, 'SUI/USDT': 12.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 7.0, 'LINK/USDT': 9.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 4.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 6.0, 'DOT/USDT': 3.0}
2025-07-29 00:01:51,739 - [KRAKEN] - root - INFO - Entry trade at 2025-04-23 00:00:00+00:00: SOL/USDT,PEPE/USDT
2025-07-29 00:01:51,739 - [KRAKEN] - root - INFO - Swap trade at 2025-04-24 00:00:00+00:00: SOL/USDT,PEPE/USDT -> SOL/USDT,SUI/USDT
2025-07-29 00:01:51,740 - [KRAKEN] - root - INFO - Swap trade at 2025-04-26 00:00:00+00:00: SOL/USDT,SUI/USDT -> SUI/USDT,PEPE/USDT
2025-07-29 00:01:51,740 - [KRAKEN] - root - INFO - Swap trade at 2025-05-04 00:00:00+00:00: SUI/USDT,PEPE/USDT -> SUI/USDT,AAVE/USDT
2025-07-29 00:01:51,740 - [KRAKEN] - root - INFO - Swap trade at 2025-05-09 00:00:00+00:00: SUI/USDT,AAVE/USDT -> SUI/USDT,PEPE/USDT
2025-07-29 00:01:51,740 - [KRAKEN] - root - INFO - Swap trade at 2025-05-13 00:00:00+00:00: SUI/USDT,PEPE/USDT -> ETH/USDT,PEPE/USDT
2025-07-29 00:01:51,740 - [KRAKEN] - root - INFO - Swap trade at 2025-05-20 00:00:00+00:00: ETH/USDT,PEPE/USDT -> AAVE/USDT,PEPE/USDT
2025-07-29 00:01:51,740 - [KRAKEN] - root - INFO - Swap trade at 2025-05-31 00:00:00+00:00: AAVE/USDT,PEPE/USDT -> ETH/USDT,AAVE/USDT
2025-07-29 00:01:51,741 - [KRAKEN] - root - INFO - Swap trade at 2025-06-06 00:00:00+00:00: ETH/USDT,AAVE/USDT -> AAVE/USDT,TRX/USDT
2025-07-29 00:01:51,741 - [KRAKEN] - root - INFO - Swap trade at 2025-06-11 00:00:00+00:00: AAVE/USDT,TRX/USDT -> ETH/USDT,AAVE/USDT
2025-07-29 00:01:51,741 - [KRAKEN] - root - INFO - Swap trade at 2025-06-20 00:00:00+00:00: ETH/USDT,AAVE/USDT -> AAVE/USDT,TRX/USDT
2025-07-29 00:01:51,741 - [KRAKEN] - root - INFO - Exit trade at 2025-06-21 00:00:00+00:00 from AAVE/USDT,TRX/USDT
2025-07-29 00:01:51,741 - [KRAKEN] - root - INFO - Entry trade at 2025-07-11 00:00:00+00:00: SUI/USDT,XRP/USDT
2025-07-29 00:01:51,741 - [KRAKEN] - root - INFO - Swap trade at 2025-07-12 00:00:00+00:00: SUI/USDT,XRP/USDT -> ETH/USDT,XRP/USDT
2025-07-29 00:01:51,741 - [KRAKEN] - root - INFO - Swap trade at 2025-07-13 00:00:00+00:00: ETH/USDT,XRP/USDT -> SUI/USDT,XRP/USDT
2025-07-29 00:01:51,742 - [KRAKEN] - root - INFO - Swap trade at 2025-07-14 00:00:00+00:00: SUI/USDT,XRP/USDT -> XRP/USDT,ADA/USDT
2025-07-29 00:01:51,742 - [KRAKEN] - root - INFO - Swap trade at 2025-07-15 00:00:00+00:00: XRP/USDT,ADA/USDT -> SUI/USDT,XRP/USDT
2025-07-29 00:01:51,742 - [KRAKEN] - root - INFO - Swap trade at 2025-07-19 00:00:00+00:00: SUI/USDT,XRP/USDT -> ETH/USDT,XRP/USDT
2025-07-29 00:01:51,742 - [KRAKEN] - root - INFO - Swap trade at 2025-07-21 00:00:00+00:00: ETH/USDT,XRP/USDT -> XRP/USDT,DOGE/USDT
2025-07-29 00:01:51,742 - [KRAKEN] - root - INFO - Swap trade at 2025-07-24 00:00:00+00:00: XRP/USDT,DOGE/USDT -> ETH/USDT,DOGE/USDT
2025-07-29 00:01:51,743 - [KRAKEN] - root - INFO - Swap trade at 2025-07-27 00:00:00+00:00: ETH/USDT,DOGE/USDT -> ETH/USDT,SUI/USDT
2025-07-29 00:01:51,743 - [KRAKEN] - root - INFO - Total trades: 21 (Entries: 2, Exits: 1, Swaps: 18)
2025-07-29 00:01:51,745 - [KRAKEN] - root - INFO - Strategy execution completed in 0s
2025-07-29 00:01:51,745 - [KRAKEN] - root - INFO - DEBUG: self.elapsed_time = 0.35566091537475586 seconds
2025-07-29 00:01:51,759 - [KRAKEN] - root - INFO - Saved allocation history to allocation_history_weighted_50-50_1d_1d_with_mtpi_no_rebal_independent_incumbent_2025-02-10.csv
2025-07-29 00:01:51,759 - [KRAKEN] - root - INFO - Strategy equity curve starts at: 2025-02-10
2025-07-29 00:01:51,759 - [KRAKEN] - root - INFO - Assets included in buy-and-hold comparison:
2025-07-29 00:01:51,759 - [KRAKEN] - root - INFO -   ETH/USDT: First available date 2024-10-13
2025-07-29 00:01:51,760 - [KRAKEN] - root - INFO -   BTC/USDT: First available date 2024-10-13
2025-07-29 00:01:51,760 - [KRAKEN] - root - INFO -   SOL/USDT: First available date 2024-10-13
2025-07-29 00:01:51,760 - [KRAKEN] - root - INFO -   SUI/USDT: First available date 2024-10-13
2025-07-29 00:01:51,760 - [KRAKEN] - root - INFO -   XRP/USDT: First available date 2024-10-13
2025-07-29 00:01:51,760 - [KRAKEN] - root - INFO -   AAVE/USDT: First available date 2024-10-13
2025-07-29 00:01:51,760 - [KRAKEN] - root - INFO -   AVAX/USDT: First available date 2024-10-13
2025-07-29 00:01:51,760 - [KRAKEN] - root - INFO -   ADA/USDT: First available date 2024-10-13
2025-07-29 00:01:51,760 - [KRAKEN] - root - INFO -   LINK/USDT: First available date 2024-10-13
2025-07-29 00:01:51,760 - [KRAKEN] - root - INFO -   TRX/USDT: First available date 2024-10-13
2025-07-29 00:01:51,760 - [KRAKEN] - root - INFO -   PEPE/USDT: First available date 2024-10-13
2025-07-29 00:01:51,760 - [KRAKEN] - root - INFO -   DOGE/USDT: First available date 2024-10-13
2025-07-29 00:01:51,760 - [KRAKEN] - root - INFO -   BNB/USDT: First available date 2024-10-13
2025-07-29 00:01:51,760 - [KRAKEN] - root - INFO -   DOT/USDT: First available date 2024-10-13
2025-07-29 00:01:51,761 - [KRAKEN] - root - INFO - Using provided start date for normalization: 2025-02-10 00:00:00+00:00
2025-07-29 00:01:51,764 - [KRAKEN] - root - INFO - Normalized ETH/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-29 00:01:51,766 - [KRAKEN] - root - INFO - Normalized BTC/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-29 00:01:51,767 - [KRAKEN] - root - INFO - Normalized SOL/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-29 00:01:51,769 - [KRAKEN] - root - INFO - Normalized SUI/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-29 00:01:51,771 - [KRAKEN] - root - INFO - Normalized XRP/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-29 00:01:51,773 - [KRAKEN] - root - INFO - Normalized AAVE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-29 00:01:51,775 - [KRAKEN] - root - INFO - Normalized AVAX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-29 00:01:51,777 - [KRAKEN] - root - INFO - Normalized ADA/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-29 00:01:51,778 - [KRAKEN] - root - INFO - Normalized LINK/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-29 00:01:51,780 - [KRAKEN] - root - INFO - Normalized TRX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-29 00:01:51,783 - [KRAKEN] - root - INFO - Normalized PEPE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-29 00:01:51,785 - [KRAKEN] - root - INFO - Normalized DOGE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-29 00:01:51,787 - [KRAKEN] - root - INFO - Normalized BNB/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-29 00:01:51,789 - [KRAKEN] - root - INFO - Normalized DOT/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-29 00:01:51,792 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for ETH/USDT with 289 points
2025-07-29 00:01:51,792 - [KRAKEN] - root - INFO - ETH/USDT B&H total return: 42.76%
2025-07-29 00:01:51,795 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for BTC/USDT with 289 points
2025-07-29 00:01:51,796 - [KRAKEN] - root - INFO - BTC/USDT B&H total return: 21.18%
2025-07-29 00:01:51,798 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for SOL/USDT with 289 points
2025-07-29 00:01:51,799 - [KRAKEN] - root - INFO - SOL/USDT B&H total return: -8.66%
2025-07-29 00:01:51,801 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for SUI/USDT with 289 points
2025-07-29 00:01:51,802 - [KRAKEN] - root - INFO - SUI/USDT B&H total return: 21.53%
2025-07-29 00:01:51,805 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for XRP/USDT with 289 points
2025-07-29 00:01:51,806 - [KRAKEN] - root - INFO - XRP/USDT B&H total return: 28.87%
2025-07-29 00:01:51,809 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for AAVE/USDT with 289 points
2025-07-29 00:01:51,810 - [KRAKEN] - root - INFO - AAVE/USDT B&H total return: 14.39%
2025-07-29 00:01:51,814 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for AVAX/USDT with 289 points
2025-07-29 00:01:51,814 - [KRAKEN] - root - INFO - AVAX/USDT B&H total return: -1.79%
2025-07-29 00:01:51,817 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for ADA/USDT with 289 points
2025-07-29 00:01:51,818 - [KRAKEN] - root - INFO - ADA/USDT B&H total return: 11.39%
2025-07-29 00:01:51,821 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for LINK/USDT with 289 points
2025-07-29 00:01:51,822 - [KRAKEN] - root - INFO - LINK/USDT B&H total return: -3.67%
2025-07-29 00:01:51,825 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for TRX/USDT with 289 points
2025-07-29 00:01:51,825 - [KRAKEN] - root - INFO - TRX/USDT B&H total return: 30.95%
2025-07-29 00:01:51,828 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for PEPE/USDT with 289 points
2025-07-29 00:01:51,829 - [KRAKEN] - root - INFO - PEPE/USDT B&H total return: 24.09%
2025-07-29 00:01:51,831 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for DOGE/USDT with 289 points
2025-07-29 00:01:51,832 - [KRAKEN] - root - INFO - DOGE/USDT B&H total return: -11.46%
2025-07-29 00:01:51,835 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for BNB/USDT with 289 points
2025-07-29 00:01:51,835 - [KRAKEN] - root - INFO - BNB/USDT B&H total return: 33.20%
2025-07-29 00:01:51,838 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for DOT/USDT with 289 points
2025-07-29 00:01:51,838 - [KRAKEN] - root - INFO - DOT/USDT B&H total return: -18.16%
2025-07-29 00:01:51,841 - [KRAKEN] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-29 00:01:51,854 - [KRAKEN] - root - INFO - Configuration loaded successfully.
2025-07-29 00:01:51,974 - [KRAKEN] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-29 00:01:51,988 - [KRAKEN] - root - INFO - Configuration loaded successfully.
2025-07-29 00:01:53,451 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:01:54,341 - [KRAKEN] - root - INFO - Added ETH/USDT buy-and-hold curve with 289 points
2025-07-29 00:01:54,342 - [KRAKEN] - root - INFO - Added BTC/USDT buy-and-hold curve with 289 points
2025-07-29 00:01:54,342 - [KRAKEN] - root - INFO - Added SOL/USDT buy-and-hold curve with 289 points
2025-07-29 00:01:54,342 - [KRAKEN] - root - INFO - Added SUI/USDT buy-and-hold curve with 289 points
2025-07-29 00:01:54,342 - [KRAKEN] - root - INFO - Added XRP/USDT buy-and-hold curve with 289 points
2025-07-29 00:01:54,342 - [KRAKEN] - root - INFO - Added AAVE/USDT buy-and-hold curve with 289 points
2025-07-29 00:01:54,342 - [KRAKEN] - root - INFO - Added AVAX/USDT buy-and-hold curve with 289 points
2025-07-29 00:01:54,342 - [KRAKEN] - root - INFO - Added ADA/USDT buy-and-hold curve with 289 points
2025-07-29 00:01:54,342 - [KRAKEN] - root - INFO - Added LINK/USDT buy-and-hold curve with 289 points
2025-07-29 00:01:54,342 - [KRAKEN] - root - INFO - Added TRX/USDT buy-and-hold curve with 289 points
2025-07-29 00:01:54,342 - [KRAKEN] - root - INFO - Added PEPE/USDT buy-and-hold curve with 289 points
2025-07-29 00:01:54,342 - [KRAKEN] - root - INFO - Added DOGE/USDT buy-and-hold curve with 289 points
2025-07-29 00:01:54,342 - [KRAKEN] - root - INFO - Added BNB/USDT buy-and-hold curve with 289 points
2025-07-29 00:01:54,342 - [KRAKEN] - root - INFO - Added DOT/USDT buy-and-hold curve with 289 points
2025-07-29 00:01:54,342 - [KRAKEN] - root - INFO - Added 14 buy-and-hold curves to results
2025-07-29 00:01:54,343 - [KRAKEN] - root - INFO -   - ETH/USDT: 289 points from 2024-10-13 00:00:00+00:00 to 2025-07-28 00:00:00+00:00
2025-07-29 00:01:54,343 - [KRAKEN] - root - INFO -   - BTC/USDT: 289 points from 2024-10-13 00:00:00+00:00 to 2025-07-28 00:00:00+00:00
2025-07-29 00:01:54,343 - [KRAKEN] - root - INFO -   - SOL/USDT: 289 points from 2024-10-13 00:00:00+00:00 to 2025-07-28 00:00:00+00:00
2025-07-29 00:01:54,343 - [KRAKEN] - root - INFO -   - SUI/USDT: 289 points from 2024-10-13 00:00:00+00:00 to 2025-07-28 00:00:00+00:00
2025-07-29 00:01:54,343 - [KRAKEN] - root - INFO -   - XRP/USDT: 289 points from 2024-10-13 00:00:00+00:00 to 2025-07-28 00:00:00+00:00
2025-07-29 00:01:54,343 - [KRAKEN] - root - INFO -   - AAVE/USDT: 289 points from 2024-10-13 00:00:00+00:00 to 2025-07-28 00:00:00+00:00
2025-07-29 00:01:54,343 - [KRAKEN] - root - INFO -   - AVAX/USDT: 289 points from 2024-10-13 00:00:00+00:00 to 2025-07-28 00:00:00+00:00
2025-07-29 00:01:54,344 - [KRAKEN] - root - INFO -   - ADA/USDT: 289 points from 2024-10-13 00:00:00+00:00 to 2025-07-28 00:00:00+00:00
2025-07-29 00:01:54,344 - [KRAKEN] - root - INFO -   - LINK/USDT: 289 points from 2024-10-13 00:00:00+00:00 to 2025-07-28 00:00:00+00:00
2025-07-29 00:01:54,344 - [KRAKEN] - root - INFO -   - TRX/USDT: 289 points from 2024-10-13 00:00:00+00:00 to 2025-07-28 00:00:00+00:00
2025-07-29 00:01:54,344 - [KRAKEN] - root - INFO -   - PEPE/USDT: 289 points from 2024-10-13 00:00:00+00:00 to 2025-07-28 00:00:00+00:00
2025-07-29 00:01:54,344 - [KRAKEN] - root - INFO -   - DOGE/USDT: 289 points from 2024-10-13 00:00:00+00:00 to 2025-07-28 00:00:00+00:00
2025-07-29 00:01:54,344 - [KRAKEN] - root - INFO -   - BNB/USDT: 289 points from 2024-10-13 00:00:00+00:00 to 2025-07-28 00:00:00+00:00
2025-07-29 00:01:54,344 - [KRAKEN] - root - INFO -   - DOT/USDT: 289 points from 2024-10-13 00:00:00+00:00 to 2025-07-28 00:00:00+00:00
2025-07-29 00:01:54,375 - [KRAKEN] - root - INFO - Converting trend detection results from USDT to EUR pairs for trading
2025-07-29 00:01:54,375 - [KRAKEN] - root - INFO - Asset conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-07-29 00:01:54,378 - [KRAKEN] - root - INFO - Successfully converted best asset series from USDT to EUR pairs
2025-07-29 00:01:54,378 - [KRAKEN] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-29 00:01:54,392 - [KRAKEN] - root - INFO - Configuration loaded successfully.
2025-07-29 00:01:54,392 - [KRAKEN] - root - INFO - Loaded MTPI configuration with 8 enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-29 00:01:54,392 - [KRAKEN] - root - INFO - Using MTPI indicators from config: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-29 00:01:54,392 - [KRAKEN] - root - INFO - Combination method: consensus
2025-07-29 00:01:54,392 - [KRAKEN] - root - INFO - Calculated appropriate limit for 1d timeframe: 216 candles (minimum 180.0 candles needed for 60 length indicator)
2025-07-29 00:01:54,392 - [KRAKEN] - root - INFO - Checking cache for 1 symbols (1d)...
2025-07-29 00:01:54,412 - [KRAKEN] - root - INFO - Loaded 2170 rows of BTC/USDT data from cache (last updated: 2025-07-29)
2025-07-29 00:01:54,413 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-29
2025-07-29 00:01:54,413 - [KRAKEN] - root - INFO - Loaded 2170 rows of BTC/USDT data from cache (after filtering).
2025-07-29 00:01:54,413 - [KRAKEN] - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-07-29 00:01:54,413 - [KRAKEN] - root - INFO - Fetched BTC data: 2170 candles from 2019-08-20 00:00:00+00:00 to 2025-07-28 00:00:00+00:00
2025-07-29 00:01:54,413 - [KRAKEN] - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-07-29 00:01:54,744 - [KRAKEN] - root - INFO - Generated PGO Score signals: {-1: np.int64(996), 0: np.int64(34), 1: np.int64(1140)}
2025-07-29 00:01:54,744 - [KRAKEN] - root - INFO - PGO signal: 1
2025-07-29 00:01:54,744 - [KRAKEN] - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-07-29 00:01:54,882 - [KRAKEN] - root - INFO - Generated BB Score signals: {-1: np.int64(993), 0: np.int64(33), 1: np.int64(1144)}
2025-07-29 00:01:54,883 - [KRAKEN] - root - INFO - Bollinger Bands signal: 1
2025-07-29 00:02:01,161 - [KRAKEN] - root - INFO - Generated DWMA signals using Weighted SD method
2025-07-29 00:02:01,161 - [KRAKEN] - root - INFO - DWMA Score signal: 1
2025-07-29 00:02:02,318 - [KRAKEN] - root - INFO - Generated DEMA Supertrend signals
2025-07-29 00:02:02,318 - [KRAKEN] - root - INFO - Signal distribution: {-1: np.int64(1268), 0: np.int64(1), 1: np.int64(901)}
2025-07-29 00:02:02,318 - [KRAKEN] - root - INFO - Generated DEMA Super Score signals
2025-07-29 00:02:02,319 - [KRAKEN] - root - INFO - DEMA Super Score signal: 1
2025-07-29 00:02:03,719 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:02:05,210 - [KRAKEN] - root - INFO - Generated DPSD signals
2025-07-29 00:02:05,213 - [KRAKEN] - root - INFO - Signal distribution: {-1: np.int64(1087), 0: np.int64(87), 1: np.int64(996)}
2025-07-29 00:02:05,213 - [KRAKEN] - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-07-29 00:02:05,215 - [KRAKEN] - root - INFO - DPSD Score signal: 1
2025-07-29 00:02:05,466 - [KRAKEN] - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-07-29 00:02:05,467 - [KRAKEN] - root - INFO - Generated AAD Score signals using SMA method
2025-07-29 00:02:05,467 - [KRAKEN] - root - INFO - AAD Score signal: 1
2025-07-29 00:02:07,220 - [KRAKEN] - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-07-29 00:02:07,220 - [KRAKEN] - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-07-29 00:02:07,220 - [KRAKEN] - root - INFO - Dynamic EMA Score signal: 1
2025-07-29 00:02:09,646 - [KRAKEN] - root - INFO - Quantile DEMA Score signal: 1
2025-07-29 00:02:09,647 - [KRAKEN] - root - INFO - Individual signals: {'pgo': 1, 'bollinger_bands': 1, 'dwma_score': 1, 'dema_super_score': 1, 'dpsd_score': 1, 'aad_score': 1, 'dynamic_ema_score': 1, 'quantile_dema_score': 1}
2025-07-29 00:02:09,647 - [KRAKEN] - root - INFO - Combined MTPI signal (consensus): 1
2025-07-29 00:02:09,647 - [KRAKEN] - root - INFO - MTPI Score: 1.000000
2025-07-29 00:02:09,648 - [KRAKEN] - root - INFO - Added current MTPI score to results: 1.000000 (using 1d timeframe)
2025-07-29 00:02:09,653 - [KRAKEN] - root - INFO - Successfully converted assets_held_df from USDT to EUR pairs
2025-07-29 00:02:09,654 - [KRAKEN] - root - INFO - Latest scores extracted (USDT): {'ETH/USDT': 13.0, 'BTC/USDT': 1.0, 'SOL/USDT': 5.0, 'SUI/USDT': 11.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 12.0, 'ADA/USDT': 7.0, 'LINK/USDT': 8.0, 'TRX/USDT': 3.0, 'PEPE/USDT': 3.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 6.0, 'DOT/USDT': 2.0}
2025-07-29 00:02:09,654 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - Original USDT scores: {'ETH/USDT': 13.0, 'BTC/USDT': 1.0, 'SOL/USDT': 5.0, 'SUI/USDT': 11.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 12.0, 'ADA/USDT': 7.0, 'LINK/USDT': 8.0, 'TRX/USDT': 3.0, 'PEPE/USDT': 3.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 6.0, 'DOT/USDT': 2.0}
2025-07-29 00:02:09,654 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - Original USDT keys order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-07-29 00:02:09,655 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - Conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-07-29 00:02:09,655 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - ETH/USDT -> ETH/EUR (score: 13.0)
2025-07-29 00:02:09,655 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - BTC/USDT -> BTC/EUR (score: 1.0)
2025-07-29 00:02:09,655 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - SOL/USDT -> SOL/EUR (score: 5.0)
2025-07-29 00:02:09,655 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - SUI/USDT -> SUI/EUR (score: 11.0)
2025-07-29 00:02:09,655 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - XRP/USDT -> XRP/EUR (score: 7.0)
2025-07-29 00:02:09,655 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - AAVE/USDT -> AAVE/EUR (score: 0.0)
2025-07-29 00:02:09,655 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - AVAX/USDT -> AVAX/EUR (score: 12.0)
2025-07-29 00:02:09,655 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - ADA/USDT -> ADA/EUR (score: 7.0)
2025-07-29 00:02:09,655 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - LINK/USDT -> LINK/EUR (score: 8.0)
2025-07-29 00:02:09,655 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - TRX/USDT -> TRX/EUR (score: 3.0)
2025-07-29 00:02:09,655 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - PEPE/USDT -> PEPE/EUR (score: 3.0)
2025-07-29 00:02:09,655 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - DOGE/USDT -> DOGE/EUR (score: 9.0)
2025-07-29 00:02:09,656 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - BNB/USDT -> BNB/EUR (score: 6.0)
2025-07-29 00:02:09,656 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - DOT/USDT -> DOT/EUR (score: 2.0)
2025-07-29 00:02:09,656 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - Final EUR scores: {'ETH/EUR': 13.0, 'BTC/EUR': 1.0, 'SOL/EUR': 5.0, 'SUI/EUR': 11.0, 'XRP/EUR': 7.0, 'AAVE/EUR': 0.0, 'AVAX/EUR': 12.0, 'ADA/EUR': 7.0, 'LINK/EUR': 8.0, 'TRX/EUR': 3.0, 'PEPE/EUR': 3.0, 'DOGE/EUR': 9.0, 'BNB/EUR': 6.0, 'DOT/EUR': 2.0}
2025-07-29 00:02:09,656 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - Final EUR keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-29 00:02:09,656 - [KRAKEN] - root - INFO - Latest scores converted to EUR: {'ETH/EUR': 13.0, 'BTC/EUR': 1.0, 'SOL/EUR': 5.0, 'SUI/EUR': 11.0, 'XRP/EUR': 7.0, 'AAVE/EUR': 0.0, 'AVAX/EUR': 12.0, 'ADA/EUR': 7.0, 'LINK/EUR': 8.0, 'TRX/EUR': 3.0, 'PEPE/EUR': 3.0, 'DOGE/EUR': 9.0, 'BNB/EUR': 6.0, 'DOT/EUR': 2.0}
2025-07-29 00:02:09,665 - [KRAKEN] - root - INFO - Saved metrics to new file: Performance_Metrics/metrics_WeightedAllocation_1d_1d_weighted_50-50_assets14_since_20250722_run_20250727_174051.csv
2025-07-29 00:02:09,665 - [KRAKEN] - root - INFO - Saved performance metrics to CSV: Performance_Metrics/metrics_WeightedAllocation_1d_1d_weighted_50-50_assets14_since_20250722_run_20250727_174051.csv
2025-07-29 00:02:09,665 - [KRAKEN] - root - INFO - === RESULTS STRUCTURE DEBUGGING ===
2025-07-29 00:02:09,665 - [KRAKEN] - root - INFO - Results type: <class 'dict'>
2025-07-29 00:02:09,665 - [KRAKEN] - root - INFO - Results keys: dict_keys(['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message'])
2025-07-29 00:02:09,665 - [KRAKEN] - root - INFO - Success flag set to: True
2025-07-29 00:02:09,665 - [KRAKEN] - root - INFO - Message set to: Strategy calculation completed successfully
2025-07-29 00:02:09,666 - [KRAKEN] - root - INFO -   - strategy_equity: <class 'pandas.core.series.Series'> with 289 entries
2025-07-29 00:02:09,666 - [KRAKEN] - root - INFO -   - buy_hold_curves: dict with 14 entries
2025-07-29 00:02:09,666 - [KRAKEN] - root - INFO -   - best_asset_series: <class 'pandas.core.series.Series'> with 289 entries
2025-07-29 00:02:09,666 - [KRAKEN] - root - INFO -   - mtpi_signals: <class 'pandas.core.series.Series'> with 289 entries
2025-07-29 00:02:09,666 - [KRAKEN] - root - INFO -   - mtpi_score: <class 'float'>
2025-07-29 00:02:09,666 - [KRAKEN] - root - INFO -   - assets_held_df: <class 'pandas.core.frame.DataFrame'> with 289 entries
2025-07-29 00:02:09,666 - [KRAKEN] - root - INFO -   - performance_metrics: dict with 3 entries
2025-07-29 00:02:09,666 - [KRAKEN] - root - INFO -   - metrics_file: <class 'str'>
2025-07-29 00:02:09,666 - [KRAKEN] - root - INFO -   - mtpi_filtering_metadata: dict with 2 entries
2025-07-29 00:02:09,666 - [KRAKEN] - root - INFO -   - success: <class 'bool'>
2025-07-29 00:02:09,666 - [KRAKEN] - root - INFO -   - message: <class 'str'>
2025-07-29 00:02:09,666 - [KRAKEN] - root - ERROR - [DEBUG] STRATEGY RESULTS - Available keys: ['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message']
2025-07-29 00:02:09,667 - [KRAKEN] - root - INFO - [DEBUG] ASSET SELECTION - Extracted best_asset from best_asset_series: ETH/EUR, SUI/EUR
2025-07-29 00:02:09,668 - [KRAKEN] - root - INFO - [DEBUG] ASSET SELECTION - Last 5 entries in best_asset_series: 2025-07-24 00:00:00+00:00    ETH/EUR, DOGE/EUR
2025-07-25 00:00:00+00:00    ETH/EUR, DOGE/EUR
2025-07-26 00:00:00+00:00    ETH/EUR, DOGE/EUR
2025-07-27 00:00:00+00:00     ETH/EUR, SUI/EUR
2025-07-28 00:00:00+00:00     ETH/EUR, SUI/EUR
dtype: object
2025-07-29 00:02:09,668 - [KRAKEN] - root - ERROR - [DEBUG] ASSET SELECTION - Latest scores available: {'ETH/EUR': 13.0, 'BTC/EUR': 1.0, 'SOL/EUR': 5.0, 'SUI/EUR': 11.0, 'XRP/EUR': 7.0, 'AAVE/EUR': 0.0, 'AVAX/EUR': 12.0, 'ADA/EUR': 7.0, 'LINK/EUR': 8.0, 'TRX/EUR': 3.0, 'PEPE/EUR': 3.0, 'DOGE/EUR': 9.0, 'BNB/EUR': 6.0, 'DOT/EUR': 2.0}
2025-07-29 00:02:09,668 - [KRAKEN] - root - ERROR - [DEBUG] ASSET SELECTION - Scores dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-29 00:02:09,668 - [KRAKEN] - root - INFO - [DEBUG] TIE-BREAKING - Strategy: incumbent
2025-07-29 00:02:09,668 - [KRAKEN] - root - WARNING - [DEBUG] ASSET SELECTION - find_best_asset_for_day() called with 14 assets
2025-07-29 00:02:09,668 - [KRAKEN] - root - WARNING - [DEBUG] ASSET SELECTION - Input scores: {'ETH/EUR': 13.0, 'BTC/EUR': 1.0, 'SOL/EUR': 5.0, 'SUI/EUR': 11.0, 'XRP/EUR': 7.0, 'AAVE/EUR': 0.0, 'AVAX/EUR': 12.0, 'ADA/EUR': 7.0, 'LINK/EUR': 8.0, 'TRX/EUR': 3.0, 'PEPE/EUR': 3.0, 'DOGE/EUR': 9.0, 'BNB/EUR': 6.0, 'DOT/EUR': 2.0}
2025-07-29 00:02:09,669 - [KRAKEN] - root - WARNING - [DEBUG] ASSET SELECTION - Dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-29 00:02:09,669 - [KRAKEN] - root - WARNING - [DEBUG] ASSET SELECTION - Maximum score found: 13.0
2025-07-29 00:02:09,669 - [KRAKEN] - root - WARNING - [DEBUG] ASSET SELECTION - Assets with max score 13.0: ['ETH/EUR']
2025-07-29 00:02:09,669 - [KRAKEN] - root - WARNING - [DEBUG] ASSET SELECTION - No tie detected, single winner: ETH/EUR
2025-07-29 00:02:09,669 - [KRAKEN] - root - ERROR - [DEBUG] SELECTED BEST ASSET: ETH/EUR (score: 13.0)
2025-07-29 00:02:09,669 - [KRAKEN] - root - ERROR - [DEBUG] ASSET SELECTION - Current day's best asset: ETH/EUR (MTPI signal: 1)
2025-07-29 00:02:09,669 - [KRAKEN] - root - ERROR - [DEBUG] ASSET SELECTION - Maximum score: 13.0
2025-07-29 00:02:09,669 - [KRAKEN] - root - ERROR - [DEBUG] ASSET SELECTION - Assets with max score: ['ETH/EUR']
2025-07-29 00:02:09,669 - [KRAKEN] - root - ERROR - [DEBUG] NO TIE - UPDATING SELECTION: ETH/EUR, SUI/EUR -> ETH/EUR
2025-07-29 00:02:09,669 - [KRAKEN] - root - ERROR - [DEBUG] NO TIE - Single winner: ETH/EUR
2025-07-29 00:02:09,726 - [KRAKEN] - root - WARNING - No 'assets_held' column found in assets_held_df. Columns: Index(['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR',
       'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR',
       'BNB/EUR', 'DOT/EUR'],
      dtype='object')
2025-07-29 00:02:09,726 - [KRAKEN] - root - INFO - Last row columns: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-29 00:02:09,726 - [KRAKEN] - root - INFO - Available assets from config: ['BTC/EUR', 'ETH/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-29 00:02:09,726 - [KRAKEN] - root - INFO - Asset columns found in dataframe: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-29 00:02:09,726 - [KRAKEN] - root - INFO - Assets with non-zero allocation: ['ETH/EUR', 'SUI/EUR']
2025-07-29 00:02:09,727 - [KRAKEN] - root - INFO - All assets sorted by score: [('ETH/EUR', 13.0), ('AVAX/EUR', 12.0), ('SUI/EUR', 11.0), ('DOGE/EUR', 9.0), ('LINK/EUR', 8.0), ('XRP/EUR', 7.0), ('ADA/EUR', 7.0), ('BNB/EUR', 6.0), ('SOL/EUR', 5.0), ('TRX/EUR', 3.0), ('PEPE/EUR', 3.0), ('DOT/EUR', 2.0), ('BTC/EUR', 1.0), ('AAVE/EUR', 0.0)]
2025-07-29 00:02:09,727 - [KRAKEN] - root - INFO - Selected top 2 assets by score: ['ETH/EUR', 'AVAX/EUR']
2025-07-29 00:02:09,727 - [KRAKEN] - root - INFO - Updated assets list with top-scoring assets: ['ETH/EUR', 'AVAX/EUR']
2025-07-29 00:02:09,727 - [KRAKEN] - root - INFO - Using weighted allocation with configured weights: {'ETH/EUR': 0.5, 'AVAX/EUR': 0.5}
2025-07-29 00:02:09,727 - [KRAKEN] - root - INFO - [DEBUG] FINAL ASSET SELECTION SUMMARY:
2025-07-29 00:02:09,727 - [KRAKEN] - root - INFO - [DEBUG]   - Best asset selected: ETH/EUR
2025-07-29 00:02:09,727 - [KRAKEN] - root - INFO - [DEBUG]   - Assets held: {'ETH/EUR': 0.5, 'AVAX/EUR': 0.5}
2025-07-29 00:02:09,727 - [KRAKEN] - root - INFO - [DEBUG]   - MTPI signal: 1
2025-07-29 00:02:09,727 - [KRAKEN] - root - INFO - [DEBUG]   - Use MTPI signal: True
2025-07-29 00:02:09,727 - [KRAKEN] - root - ERROR - 🚨 ? ETH/EUR WAS SELECTED
2025-07-29 00:02:09,729 - [KRAKEN] - root - INFO - Executing multi-asset strategy with 2 assets: {'ETH/EUR': 0.5, 'AVAX/EUR': 0.5}
2025-07-29 00:02:09,729 - [KRAKEN] - root - INFO - Passing 14 asset scores to executor for replacement logic
2025-07-29 00:02:09,839 - [KRAKEN] - root - INFO - [DEBUG] PRICE - ETH/EUR: Starting get_current_price
2025-07-29 00:02:09,839 - [KRAKEN] - root - INFO - [DEBUG] PRICE - ETH/EUR: Exchange ID: kraken
2025-07-29 00:02:09,839 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Using existing exchange instance
2025-07-29 00:02:09,839 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Symbol found in exchange markets
2025-07-29 00:02:09,839 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Attempting to fetch ticker...
2025-07-29 00:02:09,914 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Ticker fetched successfully
2025-07-29 00:02:09,916 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Ticker data: {'symbol': 'ETH/EUR', 'timestamp': None, 'datetime': None, 'high': 3354.04, 'low': 3237.02, 'bid': 3269.06, 'bidVolume': 14.0, 'ask': 3269.27, 'askVolume': 9.0, 'vwap': 3299.08376, 'open': 3274.3, 'close': 3268.1, 'last': 3268.1, 'previousClose': None, 'change': -6.2, 'percentage': -0.1893534495922792, 'average': 3271.2, 'baseVolume': 10121.92120743, 'quoteVolume': 33393065.875431903, 'info': {'a': ['3269.27000', '9', '9.000'], 'b': ['3269.06000', '14', '14.000'], 'c': ['3268.10000', '0.01022563'], 'v': ['3.99822355', '10121.92120743'], 'p': ['3274.36987', '3299.08376'], 't': ['36', '24604'], 'l': ['3268.10000', '3237.02000'], 'h': ['3275.90000', '3354.04000'], 'o': '3274.30000'}, 'indexPrice': None, 'markPrice': None}
2025-07-29 00:02:09,916 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Last price: 3268.1
2025-07-29 00:02:09,917 - [KRAKEN] - root - WARNING - Adjusted amount 0.00002226 is below minimum order size 0.0001 for ETH/EUR
2025-07-29 00:02:09,917 - [KRAKEN] - root - INFO - Using minimum order size 0.0001 for ETH/EUR
2025-07-29 00:02:09,917 - [KRAKEN] - root - INFO - [DEBUG] PRICE - AVAX/EUR: Starting get_current_price
2025-07-29 00:02:09,917 - [KRAKEN] - root - INFO - [DEBUG] PRICE - AVAX/EUR: Exchange ID: kraken
2025-07-29 00:02:09,917 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - AVAX/EUR: Using existing exchange instance
2025-07-29 00:02:09,917 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - AVAX/EUR: Symbol found in exchange markets
2025-07-29 00:02:09,917 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - AVAX/EUR: Attempting to fetch ticker...
2025-07-29 00:02:10,884 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - AVAX/EUR: Ticker fetched successfully
2025-07-29 00:02:10,884 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - AVAX/EUR: Ticker data: {'symbol': 'AVAX/EUR', 'timestamp': None, 'datetime': None, 'high': 23.47, 'low': 21.56, 'bid': 21.67, 'bidVolume': 351.0, 'ask': 21.68, 'askVolume': 1.0, 'vwap': 22.63308, 'open': 21.74, 'close': 21.73, 'last': 21.73, 'previousClose': None, 'change': -0.01, 'percentage': -0.045998160073597, 'average': 21.73, 'baseVolume': 42664.6677464, 'quoteVolume': 965632.8382776909, 'info': {'a': ['21.68000', '1', '1.000'], 'b': ['21.67000', '351', '351.000'], 'c': ['21.73000', '12.99996318'], 'v': ['14.53028953', '42664.66774640'], 'p': ['21.73050', '22.63308'], 't': ['3', '1700'], 'l': ['21.72000', '21.56000'], 'h': ['21.74000', '23.47000'], 'o': '21.74000'}, 'indexPrice': None, 'markPrice': None}
2025-07-29 00:02:10,884 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - AVAX/EUR: Last price: 21.73
2025-07-29 00:02:10,884 - [KRAKEN] - root - WARNING - Order value 0.07273450 is below minimum order value 4.0 for AVAX/EUR
2025-07-29 00:02:10,886 - [KRAKEN] - root - INFO - Using minimum amount for cost 0.18407731 for AVAX/EUR
2025-07-29 00:02:10,886 - [KRAKEN] - root - INFO - Original assets with weights: {'ETH/EUR': 0.5, 'AVAX/EUR': 0.5}
2025-07-29 00:02:14,034 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:02:15,810 - [KRAKEN] - root - WARNING - Error getting price for ETH.F/EUR: kraken does not have market symbol ETH.F/EUR
2025-07-29 00:02:16,773 - [KRAKEN] - root - WARNING - Error getting price for SOL.F/EUR: kraken does not have market symbol SOL.F/EUR
2025-07-29 00:02:16,875 - [KRAKEN] - root - WARNING - Error getting price for SUI.F/EUR: kraken does not have market symbol SUI.F/EUR
2025-07-29 00:02:16,975 - [KRAKEN] - root - WARNING - Error getting price for TRX.F/EUR: kraken does not have market symbol TRX.F/EUR
2025-07-29 00:02:18,774 - [KRAKEN] - root - INFO - Portfolio sync: Found untracked position CVX/EUR with 359.67557154 units
2025-07-29 00:02:18,777 - [KRAKEN] - root - INFO - Using provided asset scores for replacement logic: {'ETH/EUR': 13.0, 'BTC/EUR': 1.0, 'SOL/EUR': 5.0, 'SUI/EUR': 11.0, 'XRP/EUR': 7.0, 'AAVE/EUR': 0.0, 'AVAX/EUR': 12.0, 'ADA/EUR': 7.0, 'LINK/EUR': 8.0, 'TRX/EUR': 3.0, 'PEPE/EUR': 3.0, 'DOGE/EUR': 9.0, 'BNB/EUR': 6.0, 'DOT/EUR': 2.0}
2025-07-29 00:02:18,778 - [KRAKEN] - root - INFO - Top assets by score:
2025-07-29 00:02:18,778 - [KRAKEN] - root - INFO -   ETH/EUR: score=13.0, daily trade limit: OK
2025-07-29 00:02:18,778 - [KRAKEN] - root - INFO -   AVAX/EUR: score=12.0, daily trade limit: OK
2025-07-29 00:02:18,778 - [KRAKEN] - root - INFO -   SUI/EUR: score=11.0, daily trade limit: OK
2025-07-29 00:02:18,778 - [KRAKEN] - root - INFO -   DOGE/EUR: score=9.0, daily trade limit: OK
2025-07-29 00:02:18,778 - [KRAKEN] - root - INFO -   LINK/EUR: score=8.0, daily trade limit: OK
2025-07-29 00:02:18,778 - [KRAKEN] - root - INFO -   XRP/EUR: score=7.0, daily trade limit: OK
2025-07-29 00:02:18,778 - [KRAKEN] - root - INFO -   ADA/EUR: score=7.0, daily trade limit: OK
2025-07-29 00:02:18,778 - [KRAKEN] - root - INFO -   BNB/EUR: score=6.0, daily trade limit: OK
2025-07-29 00:02:18,778 - [KRAKEN] - root - INFO -   SOL/EUR: score=5.0, daily trade limit: OK
2025-07-29 00:02:18,778 - [KRAKEN] - root - INFO -   TRX/EUR: score=3.0, daily trade limit: OK
2025-07-29 00:02:18,778 - [KRAKEN] - root - INFO - Incremented daily trade counter for ETH/EUR: 1/5
2025-07-29 00:02:18,778 - [KRAKEN] - root - INFO - Incremented daily trade counter for AVAX/EUR: 1/5
2025-07-29 00:02:18,778 - [KRAKEN] - root - INFO - No assets were rejected during execution
2025-07-29 00:02:24,296 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:02:27,764 - [KRAKEN] - root - WARNING - Error getting price for ETH.F/EUR: kraken does not have market symbol ETH.F/EUR
2025-07-29 00:02:28,793 - [KRAKEN] - root - WARNING - Error getting price for SOL.F/EUR: kraken does not have market symbol SOL.F/EUR
2025-07-29 00:02:28,893 - [KRAKEN] - root - WARNING - Error getting price for SUI.F/EUR: kraken does not have market symbol SUI.F/EUR
2025-07-29 00:02:28,993 - [KRAKEN] - root - WARNING - Error getting price for TRX.F/EUR: kraken does not have market symbol TRX.F/EUR
2025-07-29 00:02:30,776 - [KRAKEN] - root - INFO - [DEBUG] PRICE - CVX/EUR: Starting get_current_price
2025-07-29 00:02:30,779 - [KRAKEN] - root - INFO - [DEBUG] PRICE - CVX/EUR: Exchange ID: kraken
2025-07-29 00:02:30,779 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - CVX/EUR: Using existing exchange instance
2025-07-29 00:02:30,779 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - CVX/EUR: Symbol found in exchange markets
2025-07-29 00:02:30,779 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - CVX/EUR: Attempting to fetch ticker...
2025-07-29 00:02:30,847 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - CVX/EUR: Ticker fetched successfully
2025-07-29 00:02:30,850 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - CVX/EUR: Ticker data: {'symbol': 'CVX/EUR', 'timestamp': None, 'datetime': None, 'high': 4.903, 'low': 4.434, 'bid': 4.577, 'bidVolume': 400.0, 'ask': 4.591, 'askVolume': 400.0, 'vwap': 4.6102, 'open': 4.806, 'close': 4.664, 'last': 4.664, 'previousClose': None, 'change': -0.142, 'percentage': -2.9546400332917186, 'average': 4.735, 'baseVolume': 13107.86148006, 'quoteVolume': 60429.86299537261, 'info': {'a': ['4.59100', '400', '400.000'], 'b': ['4.57700', '400', '400.000'], 'c': ['4.66400', '5.59671473'], 'v': ['13124.09795247', '13107.86148006'], 'p': ['4.61044', '4.61020'], 't': ['230', '228'], 'l': ['4.43400', '4.43400'], 'h': ['4.90300', '4.90300'], 'o': '4.80600'}, 'indexPrice': None, 'markPrice': None}
2025-07-29 00:02:30,850 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - CVX/EUR: Last price: 4.664
2025-07-29 00:02:30,850 - [KRAKEN] - root - INFO - Total portfolio value (cached): 1677.67306566 EUR
2025-07-29 00:02:30,851 - [KRAKEN] - root - INFO - Using safe available balance: 0.14618538 (99.99% of 0.14620000)
2025-07-29 00:02:30,851 - [KRAKEN] - root - INFO - [DEBUG] PRICE - ETH/EUR: Starting get_current_price
2025-07-29 00:02:30,851 - [KRAKEN] - root - INFO - [DEBUG] PRICE - ETH/EUR: Exchange ID: kraken
2025-07-29 00:02:30,851 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Using existing exchange instance
2025-07-29 00:02:30,851 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Symbol found in exchange markets
2025-07-29 00:02:30,851 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Attempting to fetch ticker...
2025-07-29 00:02:31,812 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Ticker fetched successfully
2025-07-29 00:02:31,813 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Ticker data: {'symbol': 'ETH/EUR', 'timestamp': None, 'datetime': None, 'high': 3354.04, 'low': 3237.02, 'bid': 3267.75, 'bidVolume': 1.0, 'ask': 3268.61, 'askVolume': 1.0, 'vwap': 3299.08404, 'open': 3274.3, 'close': 3268.1, 'last': 3268.1, 'previousClose': None, 'change': -6.2, 'percentage': -0.1893534495922792, 'average': 3271.2, 'baseVolume': 10121.59047083, 'quoteVolume': 33391977.581731338, 'info': {'a': ['3268.61000', '1', '1.000'], 'b': ['3267.75000', '1', '1.000'], 'c': ['3268.10000', '0.01022563'], 'v': ['3.99822355', '10121.59047083'], 'p': ['3274.36987', '3299.08404'], 't': ['36', '24601'], 'l': ['3268.10000', '3237.02000'], 'h': ['3275.90000', '3354.04000'], 'o': '3274.30000'}, 'indexPrice': None, 'markPrice': None}
2025-07-29 00:02:31,813 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Last price: 3268.1
2025-07-29 00:02:31,813 - [KRAKEN] - root - WARNING - Adjusted amount 0.00002225 is below minimum order size 0.0001 for ETH/EUR
2025-07-29 00:02:31,813 - [KRAKEN] - root - INFO - Using minimum order size 0.0001 for ETH/EUR
2025-07-29 00:02:31,813 - [KRAKEN] - root - INFO - Asset ETH/EUR: weight=0.5000, price=3268.10000000, required=0.32811724, amount=0.00010000
2025-07-29 00:02:31,813 - [KRAKEN] - root - INFO - [DEBUG] PRICE - AVAX/EUR: Starting get_current_price
2025-07-29 00:02:31,813 - [KRAKEN] - root - INFO - [DEBUG] PRICE - AVAX/EUR: Exchange ID: kraken
2025-07-29 00:02:31,813 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - AVAX/EUR: Using existing exchange instance
2025-07-29 00:02:31,813 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - AVAX/EUR: Symbol found in exchange markets
2025-07-29 00:02:31,813 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - AVAX/EUR: Attempting to fetch ticker...
2025-07-29 00:02:32,829 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - AVAX/EUR: Ticker fetched successfully
2025-07-29 00:02:32,832 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - AVAX/EUR: Ticker data: {'symbol': 'AVAX/EUR', 'timestamp': None, 'datetime': None, 'high': 23.47, 'low': 21.56, 'bid': 21.66, 'bidVolume': 187.0, 'ask': 21.67, 'askVolume': 291.0, 'vwap': 22.63308, 'open': 21.74, 'close': 21.73, 'last': 21.73, 'previousClose': None, 'change': -0.01, 'percentage': -0.045998160073597, 'average': 21.73, 'baseVolume': 42664.6677464, 'quoteVolume': 965632.8382776909, 'info': {'a': ['21.67000', '291', '291.000'], 'b': ['21.66000', '187', '187.000'], 'c': ['21.73000', '12.99996318'], 'v': ['14.53028953', '42664.66774640'], 'p': ['21.73050', '22.63308'], 't': ['3', '1700'], 'l': ['21.72000', '21.56000'], 'h': ['21.74000', '23.47000'], 'o': '21.74000'}, 'indexPrice': None, 'markPrice': None}
2025-07-29 00:02:32,832 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - AVAX/EUR: Last price: 21.73
2025-07-29 00:02:32,832 - [KRAKEN] - root - WARNING - Order value 0.07272723 is below minimum order value 4.0 for AVAX/EUR
2025-07-29 00:02:32,833 - [KRAKEN] - root - INFO - Using minimum amount for cost 0.18407731 for AVAX/EUR
2025-07-29 00:02:32,833 - [KRAKEN] - root - INFO - Asset AVAX/EUR: weight=0.5000, price=21.73000000, required=4.01600000, amount=0.18407731
2025-07-29 00:02:32,833 - [KRAKEN] - root - WARNING - Insufficient balance for all planned buys. Required: 4.34411724, Available: 0.14618538
2025-07-29 00:02:32,833 - [KRAKEN] - root - INFO - Sorted assets for purchase (by weight, then name): ['ETH/EUR', 'AVAX/EUR']
2025-07-29 00:02:32,833 - [KRAKEN] - root - WARNING - Cannot afford to buy ETH/EUR with weight 50.0%, requires 0.32811724
2025-07-29 00:02:32,833 - [KRAKEN] - root - WARNING - Cannot afford to buy AVAX/EUR with weight 50.0%, requires 4.01600000
2025-07-29 00:02:32,833 - [KRAKEN] - root - ERROR - Cannot afford to buy any assets with current balance
2025-07-29 00:02:33,798 - [KRAKEN] - root - INFO - Multi-asset trade result logged to trade log file
2025-07-29 00:02:33,798 - [KRAKEN] - root - ERROR - Multi-asset trade execution failed completely: {'success': False, 'trades': [], 'errors': [{'asset': 'ETH/EUR', 'action': 'buy', 'reason': 'Insufficient balance'}, {'asset': 'AVAX/EUR', 'action': 'buy', 'reason': 'Insufficient balance'}], 'rejected_assets': {}}
2025-07-29 00:02:33,850 - [KRAKEN] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/sendMessage "HTTP/1.1 400 Bad Request"
2025-07-29 00:02:33,853 - [KRAKEN] - root - WARNING - Failed to send with Markdown formatting: Can't parse entities: can't find end of the entity starting at byte offset 70
2025-07-29 00:02:33,893 - [KRAKEN] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/sendMessage "HTTP/1.1 200 OK"
2025-07-29 00:02:33,897 - [KRAKEN] - root - INFO - Asset selection logged: 2 assets selected with weighted_custom allocation
2025-07-29 00:02:33,898 - [KRAKEN] - root - INFO - Asset scores (sorted by score):
2025-07-29 00:02:33,898 - [KRAKEN] - root - INFO -   ETH/EUR: score=13.0, status=SELECTED, weight=0.50
2025-07-29 00:02:33,898 - [KRAKEN] - root - INFO -   AVAX/EUR: score=12.0, status=SELECTED, weight=0.50
2025-07-29 00:02:33,898 - [KRAKEN] - root - INFO -   SUI/EUR: score=11.0, status=NOT SELECTED, weight=0.00
2025-07-29 00:02:33,898 - [KRAKEN] - root - INFO -   DOGE/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-07-29 00:02:33,898 - [KRAKEN] - root - INFO -   LINK/EUR: score=8.0, status=NOT SELECTED, weight=0.00
2025-07-29 00:02:33,898 - [KRAKEN] - root - INFO -   XRP/EUR: score=7.0, status=NOT SELECTED, weight=0.00
2025-07-29 00:02:33,898 - [KRAKEN] - root - INFO -   ADA/EUR: score=7.0, status=NOT SELECTED, weight=0.00
2025-07-29 00:02:33,898 - [KRAKEN] - root - INFO -   BNB/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-07-29 00:02:33,898 - [KRAKEN] - root - INFO -   SOL/EUR: score=5.0, status=NOT SELECTED, weight=0.00
2025-07-29 00:02:33,898 - [KRAKEN] - root - INFO -   TRX/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-07-29 00:02:33,898 - [KRAKEN] - root - INFO -   PEPE/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-07-29 00:02:33,898 - [KRAKEN] - root - INFO -   DOT/EUR: score=2.0, status=NOT SELECTED, weight=0.00
2025-07-29 00:02:33,900 - [KRAKEN] - root - INFO -   BTC/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-07-29 00:02:33,900 - [KRAKEN] - root - INFO -   AAVE/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-07-29 00:02:33,900 - [KRAKEN] - root - INFO - Asset selection logged with 14 assets scored and 2 assets selected
2025-07-29 00:02:33,901 - [KRAKEN] - root - INFO - Extracted asset scores: {'ETH/EUR': 13.0, 'BTC/EUR': 1.0, 'SOL/EUR': 5.0, 'SUI/EUR': 11.0, 'XRP/EUR': 7.0, 'AAVE/EUR': 0.0, 'AVAX/EUR': 12.0, 'ADA/EUR': 7.0, 'LINK/EUR': 8.0, 'TRX/EUR': 3.0, 'PEPE/EUR': 3.0, 'DOGE/EUR': 9.0, 'BNB/EUR': 6.0, 'DOT/EUR': 2.0}
2025-07-29 00:02:33,901 - [KRAKEN] - root - INFO - Top 2 assets by score: ['ETH/EUR', 'AVAX/EUR']
2025-07-29 00:02:33,960 - [KRAKEN] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/sendMessage "HTTP/1.1 200 OK"
2025-07-29 00:02:33,962 - [KRAKEN] - root - INFO - Strategy execution completed successfully in 75.48 seconds
2025-07-29 00:02:33,967 - [KRAKEN] - root - INFO - Saved recovery state to data/state/recovery_state.json
2025-07-29 00:02:34,559 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:02:44,829 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:02:55,105 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:03:05,370 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:03:15,637 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:03:25,918 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:03:33,968 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:03:36,193 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:03:46,457 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:03:56,720 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:04:06,989 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:04:17,256 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:04:27,517 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:04:37,780 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:04:48,044 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:04:58,309 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:05:08,573 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:05:18,838 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:05:29,100 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:05:39,368 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:05:49,631 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:05:59,891 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:06:10,154 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:06:20,418 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:06:30,682 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:06:40,948 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:06:51,215 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:07:01,479 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:07:11,740 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:07:22,009 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:07:32,270 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:07:42,535 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:07:52,801 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:08:03,062 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:08:13,329 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:08:23,603 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:08:33,864 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:08:33,971 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:08:44,132 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:08:54,393 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:09:04,655 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:09:14,922 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:09:25,186 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:09:35,447 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:09:45,710 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-29 00:09:55,976 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
