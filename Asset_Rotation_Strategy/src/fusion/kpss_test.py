"""
KPSS (<PERSON><PERSON><PERSON><PERSON><PERSON>) Test for Fusion System

Translates PineScript KPSS stationarity test to Python.
KPSS tests the null hypothesis that a time series is stationary around a deterministic trend.
"""

import numpy as np
import pandas as pd
from typing import Optional
from .base_indicator import BaseFusionIndicator


class KPSSTest(BaseFusionIndicator):
    """KPSS test implementation for regime detection"""
    
    def __init__(self, length: int = 85):
        super().__init__("KPSS")
        self.length = length
    
    def _linear_regression(self, data: pd.Series, length: int) -> pd.Series:
        """
        Calculate linear regression values for detrending
        
        Args:
            data: Input price series
            length: Regression window length
            
        Returns:
            Linear regression trend values
        """
        linreg_values = pd.Series(index=data.index, dtype=float)
        
        for i in range(length - 1, len(data)):
            # Get window data
            window_data = data.iloc[i - length + 1:i + 1]
            x = np.arange(len(window_data))
            y = window_data.values
            
            # Calculate linear regression
            if len(x) > 1:
                # Using least squares: y = ax + b
                x_mean = np.mean(x)
                y_mean = np.mean(y)
                
                numerator = np.sum((x - x_mean) * (y - y_mean))
                denominator = np.sum((x - x_mean) ** 2)
                
                if denominator != 0:
                    slope = numerator / denominator
                    intercept = y_mean - slope * x_mean
                    
                    # Predict current value
                    linreg_values.iloc[i] = slope * (len(x) - 1) + intercept
                else:
                    linreg_values.iloc[i] = y_mean
            else:
                linreg_values.iloc[i] = y[0] if len(y) > 0 else 0
        
        return linreg_values.fillna(method='ffill').fillna(0)
    
    def _kpss_statistic(self, residuals: pd.Series, length: int) -> float:
        """
        Calculate KPSS test statistic
        
        Args:
            residuals: Detrended residuals
            length: Test window length
            
        Returns:
            KPSS test statistic
        """
        if len(residuals) < length:
            return 0.0
        
        # Get the last 'length' residuals
        test_residuals = residuals.iloc[-length:].values
        
        # Calculate partial sums
        partial_sums = np.cumsum(test_residuals)
        partial_sum_squared = np.sum(partial_sums ** 2)
        
        # Calculate number of lags for Newey-West correction
        lags = int(np.floor(np.sqrt(length)))
        
        # Calculate long-run variance with Newey-West correction
        sum_residuals = np.sum(test_residuals ** 2)
        
        # Add autocovariance terms
        for lag in range(1, lags + 1):
            if lag < length:
                weight = 1.0 - lag / (lags + 1.0)
                lag_sum = 0.0
                
                for i in range(lag, length):
                    lag_sum += test_residuals[i] * test_residuals[i - lag]
                
                sum_residuals += 2 * weight * lag_sum
        
        # Calculate long-run variance
        long_run_variance = sum_residuals / length
        
        # Prevent division by zero
        if long_run_variance <= 0:
            return 0.0
        
        # Calculate KPSS statistic
        kpss_stat = partial_sum_squared / (length * length * long_run_variance)
        
        return kpss_stat
    
    def calculate(self, data: pd.DataFrame, source_column: str = 'close', 
                 **kwargs) -> pd.Series:
        """
        Calculate normalized KPSS test values
        
        Args:
            data: OHLCV data
            source_column: Column to use for calculation (default: 'close')
            **kwargs: Additional parameters
            
        Returns:
            Normalized KPSS values between -1 and 1
        """
        if not self.validate_data(data):
            return pd.Series(dtype=float)
        
        if source_column not in data.columns:
            source_column = 'close'
        
        source = data[source_column]
        
        # Calculate linear regression trend
        linreg_values = self._linear_regression(source, self.length)
        
        # Calculate residuals (detrended series)
        residuals = source - linreg_values
        
        # Calculate rolling KPSS statistics
        kpss_stats = pd.Series(index=source.index, dtype=float)
        
        for i in range(self.length - 1, len(residuals)):
            window_residuals = residuals.iloc[max(0, i - self.length + 1):i + 1]
            kpss_stats.iloc[i] = self._kpss_statistic(window_residuals, len(window_residuals))
        
        # Forward fill initial NaN values
        kpss_stats = kpss_stats.fillna(method='ffill').fillna(0)
        
        # Dynamic normalization
        max_kpss = kpss_stats.rolling(window=self.length, min_periods=1).max()
        min_kpss = kpss_stats.rolling(window=self.length, min_periods=1).min()
        
        normalized_kpss = self.normalize(kpss_stats, max_kpss, min_kpss)
        
        return normalized_kpss.fillna(0)
    
    def get_stationarity_signal(self, data: pd.DataFrame, critical_value: float = 0.463, 
                               **kwargs) -> pd.Series:
        """
        Get stationarity signals based on KPSS critical values
        
        Args:
            data: OHLCV data
            critical_value: KPSS critical value (0.463 for 5% significance)
            **kwargs: Additional parameters
            
        Returns:
            Series with 1 for stationary, 0 for non-stationary
        """
        # Get raw KPSS statistics (before normalization)
        source = data['close']
        linreg_values = self._linear_regression(source, self.length)
        residuals = source - linreg_values
        
        kpss_stats = pd.Series(index=source.index, dtype=float)
        
        for i in range(self.length - 1, len(residuals)):
            window_residuals = residuals.iloc[max(0, i - self.length + 1):i + 1]
            kpss_stats.iloc[i] = self._kpss_statistic(window_residuals, len(window_residuals))
        
        kpss_stats = kpss_stats.fillna(method='ffill').fillna(0)
        
        # Return stationarity signal (1 if stationary, 0 if not)
        return (kpss_stats < critical_value).astype(int)
