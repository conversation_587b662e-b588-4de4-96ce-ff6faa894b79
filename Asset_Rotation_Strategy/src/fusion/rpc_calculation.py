"""
RPC (Relative Price Change) Calculation for Fusion System

Translates PineScript Relative Price Change calculation to Python.
Measures the relative change in price over time to detect regime characteristics.
"""

import numpy as np
import pandas as pd
from typing import Optional
from .base_indicator import BaseFusionIndicator


class RPCCalculation(BaseFusionIndicator):
    """Relative Price Change implementation for regime detection"""
    
    def __init__(self, length: int = 100):
        super().__init__("RPC")
        self.length = length
    
    def _calculate_relative_change(self, data: pd.Series) -> pd.Series:
        """
        Calculate relative price changes
        
        Args:
            data: Price series
            
        Returns:
            Relative change series
        """
        # Calculate period-to-period relative change
        relative_change = data.pct_change().fillna(0)
        
        return relative_change
    
    def _smooth_changes(self, changes: pd.Series, length: int) -> pd.Series:
        """
        Smooth relative changes using simple moving average
        
        Args:
            changes: Relative change series
            length: Smoothing window length
            
        Returns:
            Smoothed relative changes
        """
        return changes.rolling(window=length, min_periods=1).mean()
    
    def calculate(self, data: pd.DataFrame, source_column: str = 'close', 
                 **kwargs) -> pd.Series:
        """
        Calculate normalized Relative Price Change values
        
        Args:
            data: OHLCV data
            source_column: Column to use for calculation (default: 'close')
            **kwargs: Additional parameters
            
        Returns:
            Normalized RPC values between -1 and 1
        """
        if not self.validate_data(data):
            return pd.Series(dtype=float)
        
        if source_column not in data.columns:
            source_column = 'close'
        
        source = data[source_column]
        
        # Calculate relative changes
        relative_change = self._calculate_relative_change(source)
        
        # Smooth the changes
        smoothed_change = self._smooth_changes(relative_change, self.length)
        
        # Dynamic normalization using rolling min/max
        min_val = smoothed_change.rolling(window=self.length, min_periods=1).min()
        max_val = smoothed_change.rolling(window=self.length, min_periods=1).max()
        
        # Normalize to [-1, 1] range
        normalized_rpc = self.normalize(smoothed_change, max_val, min_val)
        
        return normalized_rpc.fillna(0)
    
    def get_volatility_regime(self, data: pd.DataFrame, low_vol_threshold: float = 0.01,
                            high_vol_threshold: float = 0.05, **kwargs) -> pd.Series:
        """
        Get volatility regime based on relative price changes
        
        Args:
            data: OHLCV data
            low_vol_threshold: Threshold for low volatility regime
            high_vol_threshold: Threshold for high volatility regime
            **kwargs: Additional parameters
            
        Returns:
            Series with volatility regime: 1 (high vol), 0 (normal), -1 (low vol)
        """
        source = data['close']
        relative_change = self._calculate_relative_change(source)
        
        # Calculate rolling volatility (standard deviation of changes)
        volatility = relative_change.rolling(window=self.length, min_periods=1).std()
        
        # Classify volatility regimes
        regime = pd.Series(0, index=source.index)  # Default to normal
        regime[volatility > high_vol_threshold] = 1   # High volatility
        regime[volatility < low_vol_threshold] = -1   # Low volatility
        
        return regime
    
    def get_trend_consistency(self, data: pd.DataFrame, **kwargs) -> pd.Series:
        """
        Get trend consistency based on relative price changes
        
        Args:
            data: OHLCV data
            **kwargs: Additional parameters
            
        Returns:
            Series with trend consistency (0-1 scale)
        """
        source = data['close']
        relative_change = self._calculate_relative_change(source)
        
        # Calculate rolling percentage of positive changes
        positive_changes = (relative_change > 0).astype(int)
        consistency = positive_changes.rolling(window=self.length, min_periods=1).mean()
        
        # Convert to consistency measure (distance from 0.5)
        # Values near 0.5 indicate inconsistent/random movement
        # Values near 0 or 1 indicate consistent directional movement
        trend_consistency = 2 * np.abs(consistency - 0.5)
        
        return trend_consistency.fillna(0)
    
    def get_momentum_strength(self, data: pd.DataFrame, **kwargs) -> pd.Series:
        """
        Get momentum strength based on cumulative relative changes
        
        Args:
            data: OHLCV data
            **kwargs: Additional parameters
            
        Returns:
            Series with momentum strength
        """
        source = data['close']
        relative_change = self._calculate_relative_change(source)
        
        # Calculate cumulative sum of changes over rolling window
        cumulative_change = relative_change.rolling(window=self.length, min_periods=1).sum()
        
        # Take absolute value to get momentum strength regardless of direction
        momentum_strength = np.abs(cumulative_change)
        
        # Normalize to 0-1 scale
        max_momentum = momentum_strength.rolling(window=self.length, min_periods=1).max()
        normalized_momentum = self.safe_divide(momentum_strength, max_momentum, 0)
        
        return normalized_momentum.fillna(0)
    
    def get_regime_classification(self, data: pd.DataFrame, 
                                trend_threshold: float = 0.6,
                                momentum_threshold: float = 0.3,
                                **kwargs) -> pd.Series:
        """
        Classify market regime based on trend consistency and momentum
        
        Args:
            data: OHLCV data
            trend_threshold: Threshold for trend consistency
            momentum_threshold: Threshold for momentum strength
            **kwargs: Additional parameters
            
        Returns:
            Series with regime classification: 1 (trending), -1 (mean reverting), 0 (neutral)
        """
        trend_consistency = self.get_trend_consistency(data, **kwargs)
        momentum_strength = self.get_momentum_strength(data, **kwargs)
        
        # Combine trend consistency and momentum for regime classification
        regime = pd.Series(0, index=data.index)  # Default to neutral
        
        # Trending: high trend consistency AND high momentum
        trending_condition = (trend_consistency > trend_threshold) & (momentum_strength > momentum_threshold)
        regime[trending_condition] = 1
        
        # Mean reverting: low trend consistency OR very low momentum
        mean_reverting_condition = (trend_consistency < (1 - trend_threshold)) | (momentum_strength < momentum_threshold/2)
        regime[mean_reverting_condition] = -1
        
        return regime
