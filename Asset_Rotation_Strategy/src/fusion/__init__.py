"""
Fusion Asset Regime Detection System

This module implements a comprehensive asset regime detection system that combines
10 different statistical and technical indicators to determine whether an asset
is in a trending or mean-reverting regime.

Components:
1. ADX (Average Directional Index)
2. KPSS (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON>-<PERSON> test)
3. ADF (Augmented Dick<PERSON>-<PERSON> test)
4. PP (Phillips-Perron test)
5. Hurst Exponent
6. PMC (Price-Momentum Correlation)
7. RPC (Relative Price Change)
8. GARCH (Generalized Autoregressive Conditional Heteroskedasticity)
9. Wavelet Transform (Haar)
10. Half-life of Mean Reversion

The system aggregates these indicators to provide a unified signal for regime detection,
helping to filter out false breakout signals and improve trading strategy performance.
"""

from .adx_indicator import ADXIndicator
from .kpss_test import KPSSTest
from .adf_test import ADFTest
from .pp_test import PPTest
from .hurst_exponent import HurstExponent
from .pmc_correlation import PMCCorrelation
from .rpc_calculation import RPCCalculation
from .garch_model import GARCHModel
from .wavelet_transform import WaveletTransform
from .halflife_calculation import HalfLifeCalculation
from .fusion_aggregator import FusionAggregator
from .fusion_visualizer import FusionVisualizer

__all__ = [
    'ADXIndicator',
    'KPSSTest',
    'ADFTest',
    'PPTest',
    'HurstExponent',
    'PMCCorrelation',
    'RPCCalculation',
    'GARCHModel',
    'WaveletTransform',
    'HalfLifeCalculation',
    'FusionAggregator',
    'FusionVisualizer'
]

__version__ = '1.0.0'
