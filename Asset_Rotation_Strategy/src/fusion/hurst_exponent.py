"""
Hurst Exponent Calculation for Fusion System

Translates PineScript Hurst Exponent calculation to Python.
Hurst Exponent measures the long-term memory of time series:
- H < 0.5: Mean reverting (anti-persistent)
- H = 0.5: Random walk
- H > 0.5: Trending (persistent)
"""

import numpy as np
import pandas as pd
from typing import Optional
from .base_indicator import BaseFusionIndicator


class HurstExponent(BaseFusionIndicator):
    """Hurst Exponent implementation for regime detection"""
    
    def __init__(self, length: int = 25, median_length: int = 25):
        super().__init__("Hurst")
        self.length = length
        self.median_length = median_length
    
    def _calculate_hurst_exponent(self, data: pd.Series, window_length: int) -> float:
        """
        Calculate Hurst Exponent for a single window using R/S analysis
        
        Args:
            data: Price data window
            window_length: Length of the window
            
        Returns:
            Hurst exponent value
        """
        if len(data) < 2:
            return 0.5  # Default to random walk
        
        # Calculate mean
        mean_val = data.mean()
        
        # Calculate cumulative deviations from mean
        cumulative_dev = (data - mean_val).cumsum()
        
        # Calculate range (R)
        R = cumulative_dev.max() - cumulative_dev.min()
        
        # Calculate standard deviation (S)
        S = data.std()
        
        # Prevent division by zero
        if S == 0 or R == 0:
            return 0.5
        
        # Calculate Hurst exponent: H = log(R/S) / log(n)
        try:
            hurst = np.log(R / S) / np.log(window_length)
            
            # Clamp to reasonable bounds
            hurst = np.clip(hurst, 0.0, 1.0)
            
            return hurst
        except (ValueError, ZeroDivisionError):
            return 0.5
    
    def _rolling_median(self, series: pd.Series, window: int) -> pd.Series:
        """
        Calculate rolling median
        
        Args:
            series: Input series
            window: Rolling window size
            
        Returns:
            Rolling median series
        """
        return series.rolling(window=window, min_periods=1).median()
    
    def calculate(self, data: pd.DataFrame, source_column: str = 'close', 
                 **kwargs) -> pd.Series:
        """
        Calculate normalized Hurst Exponent values
        
        Args:
            data: OHLCV data
            source_column: Column to use for calculation (default: 'close')
            **kwargs: Additional parameters
            
        Returns:
            Normalized Hurst values between -1 and 1
        """
        if not self.validate_data(data):
            return pd.Series(dtype=float)
        
        if source_column not in data.columns:
            source_column = 'close'
        
        source = data[source_column]
        
        # Calculate rolling Hurst exponent
        hurst_values = pd.Series(index=source.index, dtype=float)
        
        for i in range(self.length - 1, len(source)):
            # Get window data
            window_data = source.iloc[max(0, i - self.length + 1):i + 1]
            
            # Calculate Hurst exponent for this window
            hurst_exp = self._calculate_hurst_exponent(window_data, len(window_data))
            hurst_values.iloc[i] = hurst_exp
        
        # Forward fill initial NaN values
        hurst_values = hurst_values.fillna(method='ffill').fillna(0.5)
        
        # Apply rolling median filter for smoothing
        smoothed_hurst = self._rolling_median(hurst_values, self.median_length)
        
        # Dynamic normalization
        max_hurst = smoothed_hurst.rolling(window=self.length, min_periods=1).max()
        min_hurst = smoothed_hurst.rolling(window=self.length, min_periods=1).min()
        
        normalized_hurst = self.normalize(smoothed_hurst, max_hurst, min_hurst)
        
        return normalized_hurst.fillna(0)
    
    def get_regime_classification(self, data: pd.DataFrame, **kwargs) -> pd.Series:
        """
        Get regime classification based on Hurst exponent values
        
        Args:
            data: OHLCV data
            **kwargs: Additional parameters
            
        Returns:
            Series with regime classification: 1 (trending), -1 (mean reverting), 0 (random)
        """
        source = data['close']
        
        # Calculate raw Hurst values
        hurst_values = pd.Series(index=source.index, dtype=float)
        
        for i in range(self.length - 1, len(source)):
            window_data = source.iloc[max(0, i - self.length + 1):i + 1]
            hurst_exp = self._calculate_hurst_exponent(window_data, len(window_data))
            hurst_values.iloc[i] = hurst_exp
        
        hurst_values = hurst_values.ffill().fillna(0.5)
        smoothed_hurst = self._rolling_median(hurst_values, self.median_length)
        
        # Classify regimes
        regime = pd.Series(0, index=source.index)  # Default to random
        regime[smoothed_hurst > 0.55] = 1   # Trending (persistent)
        regime[smoothed_hurst < 0.45] = -1  # Mean reverting (anti-persistent)
        
        return regime
    
    def get_persistence_strength(self, data: pd.DataFrame, **kwargs) -> pd.Series:
        """
        Get persistence strength (distance from 0.5)
        
        Args:
            data: OHLCV data
            **kwargs: Additional parameters
            
        Returns:
            Series with persistence strength (0-0.5 scale)
        """
        source = data['close']
        
        hurst_values = pd.Series(index=source.index, dtype=float)
        
        for i in range(self.length - 1, len(source)):
            window_data = source.iloc[max(0, i - self.length + 1):i + 1]
            hurst_exp = self._calculate_hurst_exponent(window_data, len(window_data))
            hurst_values.iloc[i] = hurst_exp
        
        hurst_values = hurst_values.fillna(method='ffill').fillna(0.5)
        smoothed_hurst = self._rolling_median(hurst_values, self.median_length)
        
        # Calculate distance from 0.5 (random walk)
        persistence_strength = np.abs(smoothed_hurst - 0.5)
        
        return persistence_strength.fillna(0)
