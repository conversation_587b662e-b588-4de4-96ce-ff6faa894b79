#!/usr/bin/env python3
"""
Debug script to investigate portfolio tracking issues in the Kraken strategy.
This script will help identify discrepancies between what the system thinks it holds
vs what is actually held on the exchange.
"""

import sys
import os
import logging

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.trading.executor import TradingExecutor
from src.config_manager import get_exchange_credentials

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def debug_portfolio_tracking():
    """Debug portfolio tracking discrepancies."""
    
    print("=== PORTFOLIO TRACKING DEBUGGING ===")
    
    try:
        # Initialize trading executor for Kraken
        executor = TradingExecutor(
            exchange_id='kraken',
            test_mode=False,
            config_path='config/settings_kraken_eur.yaml'
        )
        
        print("\n1. INTERNAL PORTFOLIO TRACKING:")
        print("-" * 50)
        print(f"Current asset: {executor.current_asset}")
        print(f"Current portfolio: {executor.current_portfolio}")
        print(f"Last trade time: {executor.last_trade_time}")
        
        print("\n2. RAW EXCHANGE BALANCES:")
        print("-" * 50)

        # Get raw balances first
        if executor.trading_config.get('mode') == 'paper':
            raw_balances = executor.paper_trading.get_balance()
            print(f"Paper trading balances: {raw_balances}")
        else:
            raw_balances = executor.account_manager.get_all_balances()
            print(f"Raw exchange balances: {raw_balances}")

            # Show which currencies we're trying to convert to positions
            quote_currency = executor.detect_quote_currency({'ETH/EUR': 0.5, 'SUI/EUR': 0.5})
            print(f"Quote currency: {quote_currency}")

            quote_currencies = ['USDC', 'USD', 'BUSD', 'USDT', 'EUR']
            print(f"Excluded quote currencies: {quote_currencies}")

            print("\nCurrency -> Symbol conversion:")
            for currency, amount in raw_balances.items():
                if currency not in quote_currencies and amount > 0:
                    symbol = f"{currency}/{quote_currency}"
                    print(f"  {currency} ({amount:.8f}) -> {symbol}")

        print("\n3. ACTUAL EXCHANGE POSITIONS:")
        print("-" * 50)

        # Get actual positions from exchange
        if executor.trading_config.get('mode') == 'paper':
            actual_positions = executor.paper_trading.get_positions()
            balance = executor.paper_trading.get_balance()
        else:
            actual_positions = executor.account_manager.get_open_positions()
            quote_currency = executor.detect_quote_currency({'ETH/EUR': 0.5, 'SUI/EUR': 0.5})
            balance = {quote_currency: executor.account_manager.get_balance(quote_currency)}

        print(f"Available balance: {balance}")
        print(f"Actual positions: {len(actual_positions)}")

        for symbol, position in actual_positions.items():
            amount = position.get('amount', 0.0)
            value = position.get('value_usdt', 0.0)
            print(f"  {symbol}: {amount:.8f} units (value: {value:.8f})")
        
        print("\n4. DISCREPANCY ANALYSIS:")
        print("-" * 50)
        
        # Check for discrepancies
        tracked_assets = set(executor.current_portfolio.keys())
        actual_assets = set(actual_positions.keys())
        
        # Assets we think we hold but don't actually hold
        phantom_assets = tracked_assets - actual_assets
        if phantom_assets:
            print(f"❌ PHANTOM ASSETS (tracked but not held): {phantom_assets}")
            for asset in phantom_assets:
                weight = executor.current_portfolio[asset]
                print(f"   {asset}: weight={weight:.4f}")
        
        # Assets we actually hold but aren't tracking
        untracked_assets = actual_assets - tracked_assets
        if untracked_assets:
            print(f"⚠️ UNTRACKED ASSETS (held but not tracked): {untracked_assets}")
            for asset in untracked_assets:
                amount = actual_positions[asset].get('amount', 0.0)
                print(f"   {asset}: amount={amount:.8f}")
        
        # Assets that match
        matching_assets = tracked_assets.intersection(actual_assets)
        if matching_assets:
            print(f"✅ MATCHING ASSETS: {matching_assets}")
        
        if not phantom_assets and not untracked_assets:
            print("✅ Portfolio tracking is consistent!")
        
        print("\n5. SIMULATION: STRATEGY CHANGE DETECTION:")
        print("-" * 50)
        
        # Simulate what would happen with a strategy change
        # From ETH/EUR + SUI/EUR to ETH/EUR + AVAX/EUR
        old_allocation = {'ETH/EUR': 0.5, 'SUI/EUR': 0.5}
        new_allocation = {'ETH/EUR': 0.5, 'AVAX/EUR': 0.5}
        
        print(f"Simulating change from: {old_allocation}")
        print(f"                    to: {new_allocation}")
        
        # What the system THINKS it should do based on current_portfolio
        current_assets_tracked = set(executor.current_portfolio.keys())
        new_assets = set(new_allocation.keys())
        
        assets_to_sell_tracked = current_assets_tracked - new_assets
        assets_to_keep_tracked = current_assets_tracked.intersection(new_assets)
        assets_to_buy_tracked = new_assets - current_assets_tracked
        
        print(f"\nBased on TRACKED portfolio:")
        print(f"  Assets to sell: {assets_to_sell_tracked}")
        print(f"  Assets to keep: {assets_to_keep_tracked}")
        print(f"  Assets to buy: {assets_to_buy_tracked}")
        
        # What the system SHOULD do based on actual positions
        current_assets_actual = set(actual_positions.keys())
        
        assets_to_sell_actual = current_assets_actual - new_assets
        assets_to_keep_actual = current_assets_actual.intersection(new_assets)
        assets_to_buy_actual = new_assets - current_assets_actual
        
        print(f"\nBased on ACTUAL positions:")
        print(f"  Assets to sell: {assets_to_sell_actual}")
        print(f"  Assets to keep: {assets_to_keep_actual}")
        print(f"  Assets to buy: {assets_to_buy_actual}")
        
        # Check for discrepancies in the strategy logic
        if assets_to_sell_tracked != assets_to_sell_actual:
            print(f"\n❌ SELL DISCREPANCY DETECTED!")
            print(f"   Tracked would sell: {assets_to_sell_tracked}")
            print(f"   Should actually sell: {assets_to_sell_actual}")
            print(f"   Missing sells: {assets_to_sell_actual - assets_to_sell_tracked}")
        
        print("\n6. PORTFOLIO SYNC TEST:")
        print("-" * 50)
        
        # Test the sync function
        print("Running _sync_portfolio_state()...")
        executor._sync_portfolio_state()
        
        print(f"After sync - Current portfolio: {executor.current_portfolio}")
        
        # Check if sync fixed the discrepancies
        tracked_assets_after = set(executor.current_portfolio.keys())
        phantom_assets_after = tracked_assets_after - actual_assets
        untracked_assets_after = actual_assets - tracked_assets_after
        
        if phantom_assets_after:
            print(f"❌ Still have phantom assets after sync: {phantom_assets_after}")
        else:
            print("✅ No phantom assets after sync")
        
        if untracked_assets_after:
            print(f"⚠️ Still have untracked assets after sync: {untracked_assets_after}")
        else:
            print("✅ All assets are tracked after sync")
        
    except Exception as e:
        print(f"Error in portfolio tracking debug: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_portfolio_tracking()
    print("\nDebugging completed!")
