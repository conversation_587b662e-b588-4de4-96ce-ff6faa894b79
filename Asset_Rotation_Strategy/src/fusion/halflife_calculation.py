"""
Half-life of Mean Reversion Calculation for Fusion System

Translates PineScript Half-life of Mean Reversion calculation to Python.
Estimates how long it takes for price deviations to decay by half, indicating mean reversion strength.
"""

import numpy as np
import pandas as pd
from typing import Optional
from .base_indicator import BaseFusionIndicator


class HalfLifeCalculation(BaseFusionIndicator):
    """Half-life of Mean Reversion implementation for regime detection"""
    
    def __init__(self, lookback: int = 100):
        super().__init__("HalfLife")
        self.lookback = lookback
    
    def _calculate_returns(self, prices: pd.Series) -> pd.Series:
        """
        Calculate percentage returns
        
        Args:
            prices: Price series
            
        Returns:
            Percentage returns series
        """
        returns = prices.pct_change() * 100
        return returns.fillna(0)
    
    def _calculate_linear_regression_slope(self, data: pd.Series, window: int) -> pd.Series:
        """
        Calculate rolling linear regression slope
        
        Args:
            data: Input data series
            window: Rolling window size
            
        Returns:
            Rolling slope series
        """
        slopes = pd.Series(index=data.index, dtype=float)
        
        for i in range(window - 1, len(data)):
            # Get window data
            y = data.iloc[i - window + 1:i + 1].values
            x = np.arange(len(y))
            
            if len(y) > 1:
                # Calculate slope using least squares
                x_mean = np.mean(x)
                y_mean = np.mean(y)
                
                numerator = np.sum((x - x_mean) * (y - y_mean))
                denominator = np.sum((x - x_mean) ** 2)
                
                if denominator != 0:
                    slope = numerator / denominator
                    slopes.iloc[i] = slope
                else:
                    slopes.iloc[i] = 0
            else:
                slopes.iloc[i] = 0
        
        return slopes.fillna(0)
    
    def _calculate_theta(self, prices: pd.Series, window: int) -> pd.Series:
        """
        Calculate theta parameter for half-life estimation
        
        Args:
            prices: Price series
            window: Rolling window size
            
        Returns:
            Theta parameter series
        """
        # Calculate linear regression slope
        slope = self._calculate_linear_regression_slope(prices, window)
        
        # Calculate rolling standard deviations
        current_std = prices.rolling(window=window, min_periods=1).std()
        lagged_std = prices.shift(1).rolling(window=window, min_periods=1).std()
        
        # Calculate theta: slope * (std_current / std_lagged)
        theta = slope * self.safe_divide(current_std, lagged_std, 1)
        
        return theta.fillna(0)
    
    def _calculate_half_life(self, theta: pd.Series) -> pd.Series:
        """
        Calculate half-life from theta parameter
        
        Args:
            theta: Theta parameter series
            
        Returns:
            Half-life series
        """
        # Half-life = ln(2) / (theta + epsilon)
        # Add small epsilon to prevent division by zero
        epsilon = 1e-9
        half_life = np.log(2) / (theta + epsilon)
        
        # Clip extreme values to reasonable bounds
        half_life = half_life.clip(-1000, 1000)
        
        return half_life
    
    def calculate(self, data: pd.DataFrame, source_column: str = 'close', 
                 **kwargs) -> pd.Series:
        """
        Calculate normalized Half-life of Mean Reversion values
        
        Args:
            data: OHLCV data
            source_column: Column to use for calculation (default: 'close')
            **kwargs: Additional parameters
            
        Returns:
            Normalized half-life values between -1 and 1 (negated as in PineScript)
        """
        if not self.validate_data(data):
            return pd.Series(dtype=float)
        
        if source_column not in data.columns:
            source_column = 'close'
        
        source = data[source_column]
        
        # Calculate theta parameter
        theta = self._calculate_theta(source, self.lookback)
        
        # Calculate half-life
        half_life = self._calculate_half_life(theta)
        
        # Dynamic normalization
        max_half_life = half_life.rolling(window=self.lookback, min_periods=1).max()
        min_half_life = half_life.rolling(window=self.lookback, min_periods=1).min()
        
        normalized_half_life = self.normalize(half_life, max_half_life, min_half_life)
        
        # Negate the result as in PineScript (shorter half-life = stronger mean reversion = more negative)
        normalized_half_life = -normalized_half_life
        
        return normalized_half_life.fillna(0)
    
    def get_mean_reversion_strength(self, data: pd.DataFrame, **kwargs) -> pd.Series:
        """
        Get mean reversion strength based on half-life
        
        Args:
            data: OHLCV data
            **kwargs: Additional parameters
            
        Returns:
            Series with mean reversion strength (0-1 scale, higher = stronger mean reversion)
        """
        source = data['close']
        theta = self._calculate_theta(source, self.lookback)
        half_life = self._calculate_half_life(theta)
        
        # Shorter half-life indicates stronger mean reversion
        # Convert to strength measure (inverse relationship)
        # Use absolute value and invert
        abs_half_life = np.abs(half_life)
        
        # Avoid division by zero
        strength = self.safe_divide(1, abs_half_life + 1, 0)
        
        # Normalize to 0-1 scale
        max_strength = strength.rolling(window=self.lookback, min_periods=1).max()
        normalized_strength = self.safe_divide(strength, max_strength, 0)
        
        return normalized_strength.fillna(0)
    
    def get_reversion_speed(self, data: pd.DataFrame, **kwargs) -> pd.Series:
        """
        Get speed of mean reversion (inverse of half-life)
        
        Args:
            data: OHLCV data
            **kwargs: Additional parameters
            
        Returns:
            Series with reversion speed
        """
        source = data['close']
        theta = self._calculate_theta(source, self.lookback)
        half_life = self._calculate_half_life(theta)
        
        # Speed is inverse of half-life (faster reversion = higher speed)
        speed = self.safe_divide(1, np.abs(half_life) + 1e-6, 0)
        
        return speed.fillna(0)
    
    def get_regime_classification(self, data: pd.DataFrame, 
                                fast_reversion_threshold: float = 0.7,
                                slow_reversion_threshold: float = 0.3,
                                **kwargs) -> pd.Series:
        """
        Classify regime based on mean reversion speed
        
        Args:
            data: OHLCV data
            fast_reversion_threshold: Threshold for fast mean reversion
            slow_reversion_threshold: Threshold for slow mean reversion
            **kwargs: Additional parameters
            
        Returns:
            Series with regime classification: 1 (trending), -1 (mean reverting), 0 (neutral)
        """
        reversion_strength = self.get_mean_reversion_strength(data, **kwargs)
        
        # Classify regimes based on reversion strength
        regime = pd.Series(0, index=data.index)  # Default to neutral
        
        # Strong mean reversion (high strength)
        regime[reversion_strength > fast_reversion_threshold] = -1
        
        # Weak mean reversion suggests trending behavior
        regime[reversion_strength < slow_reversion_threshold] = 1
        
        return regime
    
    def get_raw_half_life(self, data: pd.DataFrame, **kwargs) -> pd.Series:
        """
        Get raw half-life values (not normalized)
        
        Args:
            data: OHLCV data
            **kwargs: Additional parameters
            
        Returns:
            Raw half-life series
        """
        source = data['close']
        theta = self._calculate_theta(source, self.lookback)
        half_life = self._calculate_half_life(theta)
        
        return half_life.fillna(0)
