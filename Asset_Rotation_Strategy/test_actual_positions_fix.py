#!/usr/bin/env python3
"""
Test script to verify that the actual positions fix works correctly.
This will simulate the exact scenario from the logs.
"""

import sys
import os
import logging

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.trading.executor import TradingExecutor

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_actual_positions_fix():
    """Test that the system now uses actual positions instead of tracked positions."""
    
    print("=== TESTING ACTUAL POSITIONS FIX ===")
    
    try:
        # Initialize trading executor for Kraken
        executor = TradingExecutor(
            exchange_id='kraken',
            test_mode=False,
            config_path='config/settings_kraken_eur.yaml'
        )
        
        print("\n1. CURRENT STATE:")
        print("-" * 50)
        print(f"Internal tracking: {executor.current_portfolio}")
        
        # Get actual positions
        if executor.trading_config.get('mode') == 'paper':
            actual_positions = executor.paper_trading.get_positions()
        else:
            actual_positions = executor.account_manager.get_open_positions()
        
        actual_positions = executor.filter_nonexistent_positions(actual_positions)
        print(f"Actual positions: {list(actual_positions.keys())}")
        
        print("\n2. STRATEGY CHANGE SIMULATION:")
        print("-" * 50)
        
        # Simulate the strategy change: ETH/EUR + SUI/EUR → ETH/EUR + AVAX/EUR
        new_allocation = {
            'ETH/EUR': 0.5,
            'AVAX/EUR': 0.5
        }
        
        print(f"Target allocation: {new_allocation}")
        
        # Test the logic that determines what to sell/buy
        current_assets = set(actual_positions.keys())
        new_assets = set(new_allocation.keys())
        
        assets_to_sell = current_assets - new_assets
        assets_to_keep = current_assets.intersection(new_assets)
        assets_to_buy = new_assets - current_assets
        
        print(f"\nDecision based on ACTUAL positions:")
        print(f"  Current assets: {sorted(current_assets)}")
        print(f"  Target assets: {sorted(new_assets)}")
        print(f"  Assets to sell: {sorted(assets_to_sell)}")
        print(f"  Assets to keep: {sorted(assets_to_keep)}")
        print(f"  Assets to buy: {sorted(assets_to_buy)}")
        
        print("\n3. EXPECTED BEHAVIOR:")
        print("-" * 50)
        
        expected_to_sell = {'SUI/EUR'}
        expected_to_keep = {'ETH/EUR'}
        expected_to_buy = {'AVAX/EUR'}
        
        print(f"Expected to sell: {expected_to_sell}")
        print(f"Expected to keep: {expected_to_keep}")
        print(f"Expected to buy: {expected_to_buy}")
        
        print("\n4. VERIFICATION:")
        print("-" * 50)
        
        # Check if the logic matches expectations
        sell_correct = assets_to_sell == expected_to_sell
        keep_correct = assets_to_keep == expected_to_keep
        buy_correct = assets_to_buy == expected_to_buy
        
        print(f"✅ Sell logic correct: {sell_correct}")
        if not sell_correct:
            print(f"   Expected: {expected_to_sell}, Got: {assets_to_sell}")
        
        print(f"✅ Keep logic correct: {keep_correct}")
        if not keep_correct:
            print(f"   Expected: {expected_to_keep}, Got: {assets_to_keep}")
        
        print(f"✅ Buy logic correct: {buy_correct}")
        if not buy_correct:
            print(f"   Expected: {expected_to_buy}, Got: {assets_to_buy}")
        
        all_correct = sell_correct and keep_correct and buy_correct
        
        print(f"\n🎉 OVERALL RESULT: {'SUCCESS' if all_correct else 'FAILED'}")
        
        if all_correct:
            print("✅ The fix is working correctly!")
            print("✅ SUI/EUR will be sold as expected")
            print("✅ ETH/EUR will be kept as expected")
            print("✅ AVAX/EUR will be bought as expected")
            print("✅ CVX/EUR is completely ignored (not in rotation)")
        else:
            print("❌ The fix needs more work")
        
        print("\n5. DIRECT SWAP LOGIC TEST:")
        print("-" * 50)

        # Test the direct swap logic
        is_direct_swap = (len(assets_to_sell) == len(assets_to_buy) == 1)
        print(f"Is direct swap: {is_direct_swap}")

        if is_direct_swap:
            sold_asset = list(assets_to_sell)[0]
            new_asset = list(assets_to_buy)[0]
            print(f"Direct swap: {sold_asset} → {new_asset}")

            # Calculate expected proceeds from SUI sale
            if sold_asset in actual_positions:
                position = actual_positions[sold_asset]
                amount = position.get('amount', 0.0)
                price = executor.get_current_price(sold_asset)
                proceeds = amount * price if price > 0 else 0

                print(f"{sold_asset} position: {amount:.8f} units")
                print(f"{sold_asset} price: {price:.4f} EUR")
                print(f"Expected sale proceeds: {proceeds:.2f} EUR")

                print(f"\n✅ FIXED LOGIC:")
                print(f"   {new_asset} will receive ALL {proceeds:.2f} EUR from {sold_asset} sale")
                print(f"   No percentage-based calculation!")
                print(f"   No additional balance added!")

                # Compare with old logic
                quote_currency = executor.detect_quote_currency(new_allocation)
                if executor.trading_config.get('mode') == 'paper':
                    available_balance = executor.paper_trading.get_balance().get(quote_currency, 0.0)
                else:
                    available_balance = executor.account_manager.get_balance(quote_currency)

                total_portfolio_value = available_balance + proceeds
                old_logic_amount = total_portfolio_value * 0.5

                print(f"\n❌ OLD LOGIC (WRONG):")
                print(f"   Total portfolio value: {total_portfolio_value:.2f} EUR")
                print(f"   {new_asset} would get 50%: {old_logic_amount:.2f} EUR")
                print(f"   Difference: {proceeds - old_logic_amount:.2f} EUR lost!")

        print("\n6. BALANCE CHECK:")
        print("-" * 50)

        # Check available balance for the trade
        quote_currency = executor.detect_quote_currency(new_allocation)
        if executor.trading_config.get('mode') == 'paper':
            available_balance = executor.paper_trading.get_balance().get(quote_currency, 0.0)
        else:
            available_balance = executor.account_manager.get_balance(quote_currency)

        print(f"Available {quote_currency} balance: {available_balance:.4f}")
        print(f"✅ Available balance will remain untouched in direct swap")
        
    except Exception as e:
        print(f"Error in test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_actual_positions_fix()
    print("\nTest completed!")
