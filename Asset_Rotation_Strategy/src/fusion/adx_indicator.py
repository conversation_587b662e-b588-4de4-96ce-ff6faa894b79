"""
ADX (Average Directional Index) Indicator for Fusion System

Translates PineScript ADX calculation to Python with directional movement analysis.
ADX measures the strength of a trend without regard to trend direction.
"""

import numpy as np
import pandas as pd
from typing import Tuple, Optional
from .base_indicator import BaseFusionIndicator


class ADXIndicator(BaseFusionIndicator):
    """ADX indicator implementation for regime detection"""
    
    def __init__(self, di_length: int = 28, adx_length: int = 20):
        super().__init__("ADX")
        self.di_length = di_length
        self.adx_length = adx_length
    
    def _calculate_directional_movement(self, high: pd.Series, low: pd.Series, 
                                      length: int) -> Tuple[pd.Series, pd.Series]:
        """
        Calculate plus and minus directional movements
        
        Args:
            high: High prices
            low: Low prices  
            length: Length period for calculation
            
        Returns:
            Tuple of (plus_di, minus_di)
        """
        # Calculate price changes
        up = high.diff()
        down = -low.diff()
        
        # Calculate directional movements
        plus_dm = pd.Series(0.0, index=high.index)
        minus_dm = pd.Series(0.0, index=high.index)
        
        # Plus DM: up > down and up > 0
        plus_condition = (up > down) & (up > 0)
        plus_dm[plus_condition] = up[plus_condition]
        
        # Minus DM: down > up and down > 0  
        minus_condition = (down > up) & (down > 0)
        minus_dm[minus_condition] = down[minus_condition]
        
        # Calculate True Range
        prev_close = high.shift(1)  # Using high as proxy for close in this context
        tr1 = high - low
        tr2 = abs(high - prev_close)
        tr3 = abs(low - prev_close)
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        
        # Apply RMA (Wilder's smoothing)
        smoothed_plus_dm = self._rma(plus_dm, length)
        smoothed_minus_dm = self._rma(minus_dm, length)
        smoothed_tr = self._rma(true_range, length)
        
        # Calculate DI values
        plus_di_values = 100 * self.safe_divide(smoothed_plus_dm, smoothed_tr)
        minus_di_values = 100 * self.safe_divide(smoothed_minus_dm, smoothed_tr)

        # Ensure results are pandas Series
        if not isinstance(plus_di_values, pd.Series):
            plus_di_values = pd.Series(plus_di_values, index=high.index)
        if not isinstance(minus_di_values, pd.Series):
            minus_di_values = pd.Series(minus_di_values, index=high.index)

        return plus_di_values, minus_di_values
    
    def _rma(self, series: pd.Series, length: int) -> pd.Series:
        """
        Calculate RMA (Wilder's smoothing) - equivalent to EWM with alpha=1/length
        
        Args:
            series: Input series
            length: Smoothing length
            
        Returns:
            RMA smoothed series
        """
        alpha = 1.0 / length
        return series.ewm(alpha=alpha, adjust=False).mean()
    
    def _calculate_adx(self, plus_di: pd.Series, minus_di: pd.Series, 
                      adx_length: int) -> pd.Series:
        """
        Calculate ADX from directional indicators
        
        Args:
            plus_di: Plus directional indicator
            minus_di: Minus directional indicator
            adx_length: ADX smoothing length
            
        Returns:
            ADX values
        """
        # Calculate DX
        di_sum = plus_di + minus_di
        di_diff = abs(plus_di - minus_di)

        dx = 100 * self.safe_divide(di_diff, di_sum)

        # Ensure dx is a pandas Series
        if not isinstance(dx, pd.Series):
            dx = pd.Series(dx, index=plus_di.index)

        # Smooth DX to get ADX
        adx = self._rma(dx, adx_length)
        
        return adx
    
    def calculate(self, data: pd.DataFrame, use_btc_data: bool = False, 
                 **kwargs) -> pd.Series:
        """
        Calculate normalized ADX values
        
        Args:
            data: OHLCV data
            use_btc_data: Whether to use BTC data (for multi-asset analysis)
            **kwargs: Additional parameters
            
        Returns:
            Normalized ADX values between -1 and 1
        """
        if not self.validate_data(data):
            return pd.Series(dtype=float)
        
        # Use appropriate price data
        if use_btc_data and 'btc_high' in data.columns:
            high = data['btc_high']
            low = data['btc_low']
        else:
            high = data['high']
            low = data['low']
        
        # Calculate directional indicators
        plus_di, minus_di = self._calculate_directional_movement(
            high, low, self.di_length
        )
        
        # Calculate ADX
        adx = self._calculate_adx(plus_di, minus_di, self.adx_length)
        
        # Dynamic normalization
        max_adx = adx.rolling(window=self.adx_length, min_periods=1).max()
        min_adx = adx.rolling(window=self.adx_length, min_periods=1).min()
        
        normalized_adx = self.normalize(adx, max_adx, min_adx)
        
        return normalized_adx.fillna(0)
    
    def get_trend_strength(self, data: pd.DataFrame, **kwargs) -> pd.Series:
        """
        Get raw ADX values for trend strength analysis
        
        Args:
            data: OHLCV data
            **kwargs: Additional parameters
            
        Returns:
            Raw ADX values (0-100 scale)
        """
        if not self.validate_data(data):
            return pd.Series(dtype=float)
        
        high = data['high']
        low = data['low']
        
        plus_di, minus_di = self._calculate_directional_movement(
            high, low, self.di_length
        )
        
        adx = self._calculate_adx(plus_di, minus_di, self.adx_length)
        
        return adx.fillna(0)
