"""
Fusion Visualizer - Plotting and visualization tools for fusion regime detection

Provides comprehensive visualization capabilities to compare fusion results with TradingView
and analyze individual indicator contributions.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from typing import Dict, Optional, Any, List, Tuple
from datetime import datetime

from .fusion_aggregator import FusionAggregator, FusionConfig


class FusionVisualizer:
    """Visualization tools for fusion regime detection system"""
    
    def __init__(self, aggregator: Optional[FusionAggregator] = None):
        self.aggregator = aggregator or FusionAggregator()
        
        # Color schemes matching PineScript
        self.color_schemes = {
            "Default": {"bull": "#275aff", "bear": "#d44646", "neutral": "#c9c9c9"},
            "Original": {"bull": "#5ed99e", "bear": "#ff0000", "neutral": "#c9c9c9"},
            "Modern": {"bull": "#18e4dd", "bear": "#d02e6c", "neutral": "#c9c9c9"},
            "Warm": {"bull": "#c5c558", "bear": "#870a0a", "neutral": "#c9c9c9"},
            "Cool": {"bull": "#00a6ff", "bear": "#27004c", "neutral": "#c9c9c9"}
        }
        
        self.current_scheme = "Default"
    
    def set_color_scheme(self, scheme: str):
        """Set color scheme for plots"""
        if scheme in self.color_schemes:
            self.current_scheme = scheme
    
    def plot_fusion_overview(self, data: pd.DataFrame, title: str = "Fusion Regime Detection", 
                           figsize: Tuple[int, int] = (15, 10), **kwargs) -> plt.Figure:
        """
        Create comprehensive overview plot of fusion analysis
        
        Args:
            data: OHLCV data
            title: Plot title
            figsize: Figure size
            **kwargs: Additional parameters
            
        Returns:
            Matplotlib figure
        """
        # Calculate fusion results
        results = self.aggregator.calculate(data, **kwargs)
        
        # Create figure with subplots
        fig, axes = plt.subplots(4, 1, figsize=figsize, sharex=True)
        fig.suptitle(title, fontsize=16, fontweight='bold')
        
        # Get colors
        colors = self.color_schemes[self.current_scheme]
        
        # Plot 1: Price with regime background
        ax1 = axes[0]
        ax1.plot(data.index, data['close'], 'k-', linewidth=1, label='Price')
        
        # Add regime background
        self._add_regime_background(ax1, results, colors)
        
        ax1.set_ylabel('Price')
        ax1.set_title('Price with Regime Detection')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Plot 2: Individual indicator signals
        ax2 = axes[1]
        individual_signals = results['individual_signals']
        
        for i, (name, signal) in enumerate(individual_signals.items()):
            ax2.plot(signal.index, signal, alpha=0.7, linewidth=1, 
                    label=name.upper(), color=plt.cm.tab10(i))
        
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax2.axhline(y=self.aggregator.config.trend_threshold, color=colors['bull'], 
                   linestyle='--', alpha=0.5, label='Trend Threshold')
        ax2.axhline(y=self.aggregator.config.revert_threshold, color=colors['bear'], 
                   linestyle='--', alpha=0.5, label='Revert Threshold')
        
        ax2.set_ylabel('Signal Value')
        ax2.set_title('Individual Indicator Signals')
        ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax2.grid(True, alpha=0.3)
        
        # Plot 3: Aggregated and smoothed signals
        ax3 = axes[2]
        ax3.plot(results['aggregated_signal'].index, results['aggregated_signal'], 
                'b-', alpha=0.7, linewidth=1, label='Aggregated')
        ax3.plot(results['smoothed_signal'].index, results['smoothed_signal'], 
                'r-', linewidth=2, label='Smoothed')
        
        ax3.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax3.axhline(y=self.aggregator.config.trend_threshold, color=colors['bull'], 
                   linestyle='--', alpha=0.5)
        ax3.axhline(y=self.aggregator.config.revert_threshold, color=colors['bear'], 
                   linestyle='--', alpha=0.5)
        
        ax3.set_ylabel('Signal Value')
        ax3.set_title('Aggregated Fusion Signal')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # Plot 4: Regime signal and confidence
        ax4 = axes[3]
        regime_signal = results['regime_signal']
        confidence = self.aggregator.get_regime_confidence(data, **kwargs)
        
        # Plot regime signal as colored bars
        trending_mask = regime_signal == 1
        reverting_mask = regime_signal == -1
        neutral_mask = regime_signal == 0
        
        ax4.bar(regime_signal.index[trending_mask], regime_signal[trending_mask], 
               color=colors['bull'], alpha=0.7, label='Trending')
        ax4.bar(regime_signal.index[reverting_mask], regime_signal[reverting_mask], 
               color=colors['bear'], alpha=0.7, label='Reverting')
        ax4.bar(regime_signal.index[neutral_mask], regime_signal[neutral_mask], 
               color=colors['neutral'], alpha=0.7, label='Neutral')
        
        # Plot confidence as line
        ax4_twin = ax4.twinx()
        ax4_twin.plot(confidence.index, confidence, 'g--', alpha=0.8, linewidth=1, label='Confidence')
        ax4_twin.set_ylabel('Confidence', color='g')
        ax4_twin.set_ylim(0, 1)
        
        ax4.set_ylabel('Regime Signal')
        ax4.set_xlabel('Date')
        ax4.set_title('Regime Classification and Confidence')
        ax4.legend(loc='upper left')
        ax4_twin.legend(loc='upper right')
        ax4.grid(True, alpha=0.3)
        
        # Format x-axis
        for ax in axes:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
            ax.xaxis.set_major_locator(mdates.MonthLocator(interval=1))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        
        plt.tight_layout()
        return fig

    def _add_regime_background(self, ax: plt.Axes, results: Dict[str, Any], colors: Dict[str, str]):
        """Add regime background coloring to plot"""
        is_trending = results['is_trending']
        is_reverting = results['is_reverting']
        is_neutral = results['is_neutral']

        # Add background colors for regimes
        for i in range(len(is_trending) - 1):
            start_idx = is_trending.index[i]
            end_idx = is_trending.index[i + 1]

            if is_trending.iloc[i]:
                ax.axvspan(start_idx, end_idx, alpha=0.2, color=colors['bull'])
            elif is_reverting.iloc[i]:
                ax.axvspan(start_idx, end_idx, alpha=0.2, color=colors['bear'])
            else:
                ax.axvspan(start_idx, end_idx, alpha=0.1, color=colors['neutral'])

    def plot_individual_indicators(self, data: pd.DataFrame, indicators: Optional[List[str]] = None,
                                 figsize: Tuple[int, int] = (15, 12), **kwargs) -> plt.Figure:
        """
        Plot individual indicator signals separately

        Args:
            data: OHLCV data
            indicators: List of indicators to plot (None for all)
            figsize: Figure size
            **kwargs: Additional parameters

        Returns:
            Matplotlib figure
        """
        results = self.aggregator.calculate(data, **kwargs)
        individual_signals = results['individual_signals']

        if indicators:
            individual_signals = {k: v for k, v in individual_signals.items() if k in indicators}

        n_indicators = len(individual_signals)
        if n_indicators == 0:
            raise ValueError("No indicators to plot")

        # Calculate subplot layout
        n_cols = 2
        n_rows = (n_indicators + n_cols - 1) // n_cols

        fig, axes = plt.subplots(n_rows, n_cols, figsize=figsize, sharex=True)
        if n_rows == 1:
            axes = axes.reshape(1, -1)
        elif n_cols == 1:
            axes = axes.reshape(-1, 1)

        fig.suptitle('Individual Fusion Indicators', fontsize=16, fontweight='bold')

        colors = self.color_schemes[self.current_scheme]

        for i, (name, signal) in enumerate(individual_signals.items()):
            row = i // n_cols
            col = i % n_cols
            ax = axes[row, col]

            # Plot signal
            ax.plot(signal.index, signal, 'b-', linewidth=1.5, label=name.upper())

            # Add threshold lines
            ax.axhline(y=0, color='black', linestyle='-', alpha=0.3)
            ax.axhline(y=self.aggregator.config.trend_threshold, color=colors['bull'],
                      linestyle='--', alpha=0.5, label='Trend Threshold')
            ax.axhline(y=self.aggregator.config.revert_threshold, color=colors['bear'],
                      linestyle='--', alpha=0.5, label='Revert Threshold')

            # Add regime background
            self._add_regime_background(ax, results, colors)

            ax.set_title(f'{name.upper()} Signal')
            ax.set_ylabel('Signal Value')
            ax.legend()
            ax.grid(True, alpha=0.3)

        # Hide empty subplots
        for i in range(n_indicators, n_rows * n_cols):
            row = i // n_cols
            col = i % n_cols
            axes[row, col].set_visible(False)

        # Format x-axis for bottom row
        for col in range(n_cols):
            if n_rows > 0:
                ax = axes[n_rows - 1, col]
                if ax.get_visible():
                    ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
                    ax.xaxis.set_major_locator(mdates.MonthLocator(interval=1))
                    plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
                    ax.set_xlabel('Date')

        plt.tight_layout()
        return fig

    def plot_regime_comparison(self, data: pd.DataFrame, tradingview_data: Optional[pd.Series] = None,
                             figsize: Tuple[int, int] = (15, 8), **kwargs) -> plt.Figure:
        """
        Compare fusion regime detection with TradingView or other reference

        Args:
            data: OHLCV data
            tradingview_data: Reference regime data from TradingView
            figsize: Figure size
            **kwargs: Additional parameters

        Returns:
            Matplotlib figure
        """
        results = self.aggregator.calculate(data, **kwargs)

        fig, axes = plt.subplots(3, 1, figsize=figsize, sharex=True)
        fig.suptitle('Regime Detection Comparison', fontsize=16, fontweight='bold')

        colors = self.color_schemes[self.current_scheme]

        # Plot 1: Price with fusion regime
        ax1 = axes[0]
        ax1.plot(data.index, data['close'], 'k-', linewidth=1, label='Price')
        self._add_regime_background(ax1, results, colors)
        ax1.set_ylabel('Price')
        ax1.set_title('Price with Fusion Regime Detection')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # Plot 2: Fusion signal
        ax2 = axes[1]
        smoothed_signal = results['smoothed_signal']
        ax2.plot(smoothed_signal.index, smoothed_signal, 'b-', linewidth=2, label='Fusion Signal')
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax2.axhline(y=self.aggregator.config.trend_threshold, color=colors['bull'],
                   linestyle='--', alpha=0.5, label='Trend Threshold')
        ax2.axhline(y=self.aggregator.config.revert_threshold, color=colors['bear'],
                   linestyle='--', alpha=0.5, label='Revert Threshold')
        ax2.set_ylabel('Signal Value')
        ax2.set_title('Fusion Aggregated Signal')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # Plot 3: Comparison with reference data
        ax3 = axes[2]
        regime_signal = results['regime_signal']

        # Plot fusion regime
        ax3.plot(regime_signal.index, regime_signal, 'b-', linewidth=2,
                label='Fusion Regime', alpha=0.8)

        # Plot reference data if provided
        if tradingview_data is not None:
            ax3.plot(tradingview_data.index, tradingview_data, 'r--', linewidth=2,
                    label='TradingView Reference', alpha=0.8)

            # Calculate correlation if both series overlap
            common_index = regime_signal.index.intersection(tradingview_data.index)
            if len(common_index) > 10:
                corr = regime_signal.reindex(common_index).corr(
                    tradingview_data.reindex(common_index)
                )
                ax3.text(0.02, 0.98, f'Correlation: {corr:.3f}',
                        transform=ax3.transAxes, verticalalignment='top',
                        bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

        ax3.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax3.set_ylabel('Regime Signal')
        ax3.set_xlabel('Date')
        ax3.set_title('Regime Signal Comparison')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # Format x-axis
        for ax in axes:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
            ax.xaxis.set_major_locator(mdates.MonthLocator(interval=1))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

        plt.tight_layout()
        return fig

    def save_plot(self, fig: plt.Figure, filename: str, dpi: int = 300):
        """
        Save plot to file

        Args:
            fig: Matplotlib figure
            filename: Output filename
            dpi: Resolution for saved image
        """
        fig.savefig(filename, dpi=dpi, bbox_inches='tight',
                   facecolor='white', edgecolor='none')
        print(f"Plot saved to: {filename}")

    def create_validation_report(self, data: pd.DataFrame, output_dir: str = ".",
                               tradingview_data: Optional[pd.Series] = None,
                               **kwargs) -> Dict[str, str]:
        """
        Create comprehensive validation report with multiple plots

        Args:
            data: OHLCV data
            output_dir: Directory to save plots
            tradingview_data: Reference data for comparison
            **kwargs: Additional parameters

        Returns:
            Dictionary of saved filenames
        """
        import os
        from datetime import datetime

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        saved_files = {}

        # Create overview plot
        fig1 = self.plot_fusion_overview(data, **kwargs)
        filename1 = os.path.join(output_dir, f"fusion_overview_{timestamp}.png")
        self.save_plot(fig1, filename1)
        saved_files['overview'] = filename1
        plt.close(fig1)

        # Create individual indicators plot
        fig2 = self.plot_individual_indicators(data, **kwargs)
        filename2 = os.path.join(output_dir, f"fusion_indicators_{timestamp}.png")
        self.save_plot(fig2, filename2)
        saved_files['indicators'] = filename2
        plt.close(fig2)

        # Create comparison plot if reference data provided
        if tradingview_data is not None:
            fig3 = self.plot_regime_comparison(data, tradingview_data, **kwargs)
            filename3 = os.path.join(output_dir, f"fusion_comparison_{timestamp}.png")
            self.save_plot(fig3, filename3)
            saved_files['comparison'] = filename3
            plt.close(fig3)

        print(f"Validation report created with {len(saved_files)} plots")
        return saved_files
