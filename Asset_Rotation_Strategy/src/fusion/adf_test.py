"""
ADF (Augmented <PERSON><PERSON>-Fuller) Test for Fusion System

Translates PineScript ADF unit root test to Python.
ADF tests the null hypothesis that a unit root is present in a time series.
"""

import numpy as np
import pandas as pd
from typing import Tuple, Optional
from .base_indicator import BaseFusionIndicator


class ADFTest(BaseFusionIndicator):
    """ADF test implementation for regime detection"""
    
    def __init__(self, lookback: int = 110):
        super().__init__("ADF")
        self.lookback = lookback
        self.critical_value = -2.5  # Threshold for mean reversion
    
    def _adf_test_statistic(self, data_array: np.ndarray) -> Tuple[float, float]:
        """
        Calculate ADF test statistic (simplified version matching PineScript)
        
        Args:
            data_array: Array of price data for testing
            
        Returns:
            Tuple of (tau_statistic, critical_value)
        """
        if len(data_array) < 2:
            return 0.0, self.critical_value
        
        # Create differences and lagged values
        n_obs = len(data_array) - 1
        y_diff = []  # First differences
        y_lag = []   # Lagged levels
        
        for i in range(n_obs):
            y_diff.append(data_array[i] - data_array[i + 1])
            y_lag.append(data_array[i + 1])
        
        if len(y_diff) == 0:
            return 0.0, self.critical_value
        
        # Calculate simple average of differences (simplified tau statistic)
        tau_adf = np.mean(y_diff)
        
        return tau_adf, self.critical_value
    
    def calculate(self, data: pd.DataFrame, source_column: str = 'close', 
                 **kwargs) -> pd.Series:
        """
        Calculate normalized ADF test values
        
        Args:
            data: OHLCV data
            source_column: Column to use for calculation (default: 'close')
            **kwargs: Additional parameters
            
        Returns:
            Normalized ADF values between -1 and 1
        """
        if not self.validate_data(data):
            return pd.Series(dtype=float)
        
        if source_column not in data.columns:
            source_column = 'close'
        
        source = data[source_column]
        
        # Calculate rolling ADF statistics
        adf_stats = pd.Series(index=source.index, dtype=float)
        
        for i in range(self.lookback - 1, len(source)):
            # Get window data (most recent first, matching PineScript indexing)
            window_data = source.iloc[max(0, i - self.lookback + 1):i + 1].values
            
            # Reverse to match PineScript array indexing (newest first)
            window_data = window_data[::-1]
            
            # Calculate ADF statistic
            tau_adf, _ = self._adf_test_statistic(window_data)
            adf_stats.iloc[i] = tau_adf
        
        # Forward fill initial NaN values
        adf_stats = adf_stats.fillna(method='ffill').fillna(0)
        
        # Dynamic normalization
        max_adf = adf_stats.rolling(window=self.lookback, min_periods=1).max()
        min_adf = adf_stats.rolling(window=self.lookback, min_periods=1).min()
        
        normalized_adf = self.normalize(adf_stats, max_adf, min_adf)
        
        return normalized_adf.fillna(0)
    
    def get_unit_root_signal(self, data: pd.DataFrame, **kwargs) -> pd.Series:
        """
        Get unit root signals based on ADF critical values
        
        Args:
            data: OHLCV data
            **kwargs: Additional parameters
            
        Returns:
            Series with 1 for stationary (no unit root), 0 for non-stationary (unit root present)
        """
        source = data['close']
        
        # Calculate raw ADF statistics
        adf_stats = pd.Series(index=source.index, dtype=float)
        
        for i in range(self.lookback - 1, len(source)):
            window_data = source.iloc[max(0, i - self.lookback + 1):i + 1].values
            window_data = window_data[::-1]  # Reverse for PineScript compatibility
            
            tau_adf, _ = self._adf_test_statistic(window_data)
            adf_stats.iloc[i] = tau_adf
        
        adf_stats = adf_stats.fillna(method='ffill').fillna(0)
        
        # Return stationarity signal (1 if stationary, 0 if unit root present)
        # ADF: reject null hypothesis (unit root) if statistic < critical value
        return (adf_stats < self.critical_value).astype(int)
    
    def get_mean_reversion_strength(self, data: pd.DataFrame, **kwargs) -> pd.Series:
        """
        Get mean reversion strength based on how far ADF statistic is from critical value
        
        Args:
            data: OHLCV data
            **kwargs: Additional parameters
            
        Returns:
            Series with mean reversion strength (0-1 scale)
        """
        source = data['close']
        
        adf_stats = pd.Series(index=source.index, dtype=float)
        
        for i in range(self.lookback - 1, len(source)):
            window_data = source.iloc[max(0, i - self.lookback + 1):i + 1].values
            window_data = window_data[::-1]
            
            tau_adf, _ = self._adf_test_statistic(window_data)
            adf_stats.iloc[i] = tau_adf
        
        adf_stats = adf_stats.fillna(method='ffill').fillna(0)
        
        # Calculate strength: how much below critical value (more negative = stronger mean reversion)
        strength = np.maximum(0, self.critical_value - adf_stats)
        
        # Normalize to 0-1 scale
        max_strength = strength.rolling(window=self.lookback, min_periods=1).max()
        normalized_strength = self.safe_divide(strength, max_strength, 0)
        
        return pd.Series(normalized_strength, index=source.index).fillna(0)
