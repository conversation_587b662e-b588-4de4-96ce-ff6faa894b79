"""
PP (Phillips-Perron) Test for Fusion System

Translates PineScript Phillips-Perron test to Python.
PP test is similar to ADF but uses non-parametric statistical methods.
"""

import numpy as np
import pandas as pd
from typing import Optional
from .base_indicator import BaseFusionIndicator


class PPTest(BaseFusionIndicator):
    """Phillips-Perron test implementation for regime detection"""
    
    def __init__(self, length: int = 100):
        super().__init__("PP")
        self.length = length
    
    def _calculate_log_returns(self, prices: pd.Series) -> pd.Series:
        """
        Calculate log returns from price series
        
        Args:
            prices: Price series
            
        Returns:
            Log returns series
        """
        return np.log(prices / prices.shift(1)).fillna(0)
    
    def _rolling_correlation(self, x: pd.Series, y: pd.Series, window: int) -> pd.Series:
        """
        Calculate rolling correlation between two series
        
        Args:
            x: First series
            y: Second series  
            window: Rolling window size
            
        Returns:
            Rolling correlation series
        """
        return x.rolling(window=window).corr(y).fillna(0)
    
    def _rolling_std(self, series: pd.Series, window: int) -> pd.Series:
        """
        Calculate rolling standard deviation
        
        Args:
            series: Input series
            window: Rolling window size
            
        Returns:
            Rolling standard deviation series
        """
        return series.rolling(window=window).std().fillna(0)
    
    def _pp_statistic(self, log_returns: pd.Series, window: int) -> pd.Series:
        """
        Calculate Phillips-Perron test statistic
        
        Args:
            log_returns: Log returns series
            window: Test window length
            
        Returns:
            PP test statistic series
        """
        # Calculate rolling mean of log returns
        mean_log_returns = log_returns.rolling(window=window).mean().fillna(0)
        
        # Calculate rolling standard deviation
        std_log_returns = self._rolling_std(log_returns, window)
        
        # Calculate lagged log returns
        lagged_returns = log_returns.shift(1).fillna(0)
        lagged_std = self._rolling_std(lagged_returns, window)
        
        # Calculate correlation coefficient (beta)
        correlation = self._rolling_correlation(log_returns, lagged_returns, window)
        beta_ratio = self.safe_divide(std_log_returns, lagged_std, 0)

        # Ensure beta_ratio is a pandas Series
        if not isinstance(beta_ratio, pd.Series):
            beta_ratio = pd.Series(beta_ratio, index=log_returns.index)

        beta = correlation * beta_ratio

        # Calculate residuals
        predicted = mean_log_returns + beta * lagged_returns
        residuals = log_returns - predicted

        # Calculate PP statistic
        residual_std = self._rolling_std(residuals, window)

        # PP Z-statistic (adjusted by factor of 10 as in PineScript)
        pp_z_ratio = self.safe_divide(beta - 1, residual_std, 0)

        # Ensure pp_z_ratio is a pandas Series
        if not isinstance(pp_z_ratio, pd.Series):
            pp_z_ratio = pd.Series(pp_z_ratio, index=log_returns.index)

        pp_z = pp_z_ratio / 10

        return pp_z
    
    def calculate(self, data: pd.DataFrame, source_column: str = 'close', 
                 **kwargs) -> pd.Series:
        """
        Calculate normalized Phillips-Perron test values
        
        Args:
            data: OHLCV data
            source_column: Column to use for calculation (default: 'close')
            **kwargs: Additional parameters
            
        Returns:
            Normalized PP values between -1 and 1
        """
        if not self.validate_data(data):
            return pd.Series(dtype=float)
        
        if source_column not in data.columns:
            source_column = 'close'
        
        source = data[source_column]
        
        # Calculate log returns
        log_returns = self._calculate_log_returns(source)
        
        # Calculate PP statistic
        pp_statistic = self._pp_statistic(log_returns, self.length)
        
        # Dynamic normalization
        max_pp = pp_statistic.rolling(window=self.length, min_periods=1).max()
        min_pp = pp_statistic.rolling(window=self.length, min_periods=1).min()
        
        normalized_pp = self.normalize(pp_statistic, max_pp, min_pp)
        
        return normalized_pp.fillna(0)
    
    def get_stationarity_signal(self, data: pd.DataFrame, critical_value: float = -2.86, 
                               **kwargs) -> pd.Series:
        """
        Get stationarity signals based on PP critical values
        
        Args:
            data: OHLCV data
            critical_value: PP critical value (-2.86 for 5% significance)
            **kwargs: Additional parameters
            
        Returns:
            Series with 1 for stationary, 0 for non-stationary
        """
        source = data['close']
        log_returns = self._calculate_log_returns(source)
        pp_statistic = self._pp_statistic(log_returns, self.length)
        
        # Return stationarity signal (1 if stationary, 0 if not)
        # PP: reject null hypothesis (unit root) if statistic < critical value
        return (pp_statistic < critical_value).astype(int)
    
    def get_mean_reversion_probability(self, data: pd.DataFrame, **kwargs) -> pd.Series:
        """
        Get mean reversion probability based on PP statistic
        
        Args:
            data: OHLCV data
            **kwargs: Additional parameters
            
        Returns:
            Series with mean reversion probability (0-1 scale)
        """
        source = data['close']
        log_returns = self._calculate_log_returns(source)
        pp_statistic = self._pp_statistic(log_returns, self.length)
        
        # Convert PP statistic to probability using sigmoid-like transformation
        # More negative values indicate higher probability of mean reversion
        probability = 1 / (1 + np.exp(pp_statistic))  # Sigmoid transformation
        
        return probability.fillna(0.5)  # Default to neutral probability
