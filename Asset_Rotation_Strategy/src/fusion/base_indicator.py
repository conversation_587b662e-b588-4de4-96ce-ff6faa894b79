"""
Base class for all fusion indicators

Provides common functionality for normalization, data handling, and interface consistency.
"""

import numpy as np
import pandas as pd
from abc import ABC, abstractmethod
from typing import Union, Optional, Tuple


class BaseFusionIndicator(ABC):
    """Base class for all fusion regime detection indicators"""
    
    def __init__(self, name: str):
        self.name = name
        self._last_values = {}
        
    @staticmethod
    def normalize(value: Union[float, np.ndarray], max_value: Union[float, np.ndarray], 
                  min_value: Union[float, np.ndarray]) -> Union[float, np.ndarray]:
        """
        Normalize value to range [-1, 1]
        
        Args:
            value: Value to normalize
            max_value: Maximum value for normalization
            min_value: Minimum value for normalization
            
        Returns:
            Normalized value between -1 and 1
        """
        # Handle division by zero
        range_val = max_value - min_value
        if isinstance(range_val, (int, float)):
            if range_val == 0:
                return 0.0
        else:
            range_val = np.where(range_val == 0, 1e-10, range_val)
            
        return 2 * ((value - min_value) / range_val) - 1
    
    @staticmethod
    def rolling_window(data: pd.Series, window: int) -> np.ndarray:
        """
        Create rolling window view of data
        
        Args:
            data: Input data series
            window: Window size
            
        Returns:
            Array of rolling windows
        """
        if len(data) < window:
            return np.array([])
            
        return np.array([data.iloc[i:i+window].values for i in range(len(data) - window + 1)])
    
    @staticmethod
    def safe_divide(numerator: Union[float, np.ndarray], 
                   denominator: Union[float, np.ndarray], 
                   default: float = 0.0) -> Union[float, np.ndarray]:
        """
        Safe division with default value for zero denominator
        
        Args:
            numerator: Numerator value(s)
            denominator: Denominator value(s)
            default: Default value when denominator is zero
            
        Returns:
            Division result or default value
        """
        if isinstance(denominator, (int, float)):
            return numerator / denominator if denominator != 0 else default
        else:
            return np.where(denominator != 0, numerator / denominator, default)
    
    @abstractmethod
    def calculate(self, data: pd.DataFrame, **kwargs) -> pd.Series:
        """
        Calculate the indicator values
        
        Args:
            data: OHLCV data with columns ['open', 'high', 'low', 'close', 'volume']
            **kwargs: Additional parameters specific to each indicator
            
        Returns:
            Series of normalized indicator values
        """
        pass
    
    def get_regime_signal(self, values: pd.Series, trend_threshold: float = 0.1, 
                         revert_threshold: float = -0.1) -> pd.Series:
        """
        Convert indicator values to regime signals
        
        Args:
            values: Indicator values
            trend_threshold: Threshold for trending regime
            revert_threshold: Threshold for reverting regime
            
        Returns:
            Series with regime signals: 1 (trending), -1 (reverting), 0 (neutral)
        """
        signals = pd.Series(0, index=values.index)
        signals[values > trend_threshold] = 1
        signals[values < revert_threshold] = -1
        return signals
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        """
        Validate input data format
        
        Args:
            data: Input OHLCV data
            
        Returns:
            True if data is valid, False otherwise
        """
        required_columns = ['open', 'high', 'low', 'close']
        if not all(col in data.columns for col in required_columns):
            return False
            
        if data.empty:
            return False
            
        # Check for sufficient data
        if len(data) < 2:
            return False
            
        return True
