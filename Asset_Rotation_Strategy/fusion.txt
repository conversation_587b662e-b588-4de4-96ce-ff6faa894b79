use_btc_data = input.bool(true, title="Use BTC Data?", group="General Settings")

// Define data sources based on the toggle input
source = use_btc_data ? request.security('INDEX:BTCUSD', timeframe.period, close) : close
btcHigh = use_btc_data ? request.security('INDEX:BTCUSD', timeframe.period, high) : high
btcLow = use_btc_data ? request.security('INDEX:BTCUSD', timeframe.period, low) : low


//  <>----------------------------<>
//  |            INPUTS            |
//  <>----------------------------<>

//@description Style settings
//@param ColMode Color selection mode for the plot
ColMode   = input.string("Default", "Color Selection", group = "Style Settings", inline  = "drop", options = ["Default", "Original", "Modern", "Warm", "Cool"])

//@description Color scheme selection based on the ColMode input
[bullColor, bearColor, neutralColor] = switch ColMode
    "Default"     => [#275aff, #d44646, #c9c9c9]
    "Original"    => [#5ed99e, #ff0000, #c9c9c9]
    "Modern"      => [#18e4dd, #d02e6c, #c9c9c9]
    "Warm"        => [#c5c558, #870a0a, #c9c9c9]
    "Cool"        => [#00a6ff, #27004c, #c9c9c9]

//@description Boolean inputs to include indicators in aggSig calculation
include_adx = input(false, title="Include ADX in Fusion", group="Indicator Selection")
include_kpss = input(false, title="Include KPSS in Fusion", group="Indicator Selection")
include_adf = input(true, title="Include ADF in Fusion", group="Indicator Selection")
include_pp = input(true, title="Include PP Test in Fusion", group="Indicator Selection")
include_hurst = input(true, title="Include Hurst Exponent in Fusion", group="Indicator Selection")
include_corr = input(true, title="Include PMC Correlation in Fusion", group="Indicator Selection")
include_rpc = input(true, title="Include Relative Price Change in Fusion", group="Indicator Selection")
include_garch = input(true, title="Include GARCH Model in Fusion", group="Indicator Selection")
include_wavelet = input(true, title="Include Wavelet Transform in Fusion", group="Indicator Selection")
include_halflife = input(false, title="Include Half Life of MR in Fusion", group="Indicator Selection")

///@description General input settings
//@param threshold Signal threshold for determining the market regime
trend_threshold   = input.float(0.1, title = "Trending Threshold", tooltip = "Threshold Must Be Within 1.0 & 0.0", group = "General Settings", step = 0.01)

//@description General input settings
//@param threshold Signal threshold for determining the market regime
revert_threshold   = input.float(-0.1, title = "Reverting Threshold", tooltip = "Threshold Must Be Within 0.0 & -1.0", group = "General Settings", step = 0.01)

//@description ADX settings: smoothing period and DI length
//@param adxlen ADX smoothing period
//@param dilen DI length period
adxlen      = input(20, title="ADX Smoothing", group = "ADX Settings")
dilen       = input(28, title="DI Length", group = "ADX Settings")

//@description KPSS settings: KPSS test length configuration
//@param KPSS_LEN Length for KPSS test calculation
KPSS_LEN    = input.int(85, title="KPSS Length", group = "KPSS Settings")

//@description ADF settings: ADF lookback period configuration
//@param lookback Lookback period for ADF test
lookback    = input.int(110, title="ADF Lookback", group = "ADF Settings")

//@description PP Test settings: length configuration for the Phillips-Perron test
//@param pp_length Test length for Phillips-Perron test
pp_length   = input.int(100, title="PP Test Length", group = "PP Test Settings")

//@description Hurst Exponent settings
//@param hurst_length Length for Hurst exponent calculation
//@param hurst_median_length Rolling median filter length for smoothing
hurst_length = input.int(25, title="Hurst Exponent Length", minval=1, tooltip="Length for Hurst Exponent Calculation", group="Hurst Settings")
hurst_median_length = input.int(25, title="Rolling Median Filter Length", minval=1, tooltip="Length for Rolling Median Filter", group="Hurst Settings")

//@description PMC Inputs: select momentum indicator and correlation analysis settings
//@param momentumType Momentum indicator selection
//@param corr_length Correlation window length
momentumType = input.string("MACD", title="Momentum Indicator", options=["RSI", "Stochastic", "ROC", "MACD", "MFI", "CMO", "Williams %R", "Ultimate Oscillator", "DPO"], tooltip="Select the momentum indicator to analyze", group = "PMC Inputs")
corr_length = input.int(200, title="Correlation Window", tooltip="Window length for correlation analysis", group = "PMC Inputs")

//@description RSI Inputs: RSI configuration for PMC
//@param rsi_length RSI calculation length
//@param rsi_source RSI source for input data
rsi_length = input.int(14, title="RSI Length", group="PMC Inputs")
rsi_source = source

//@description Stochastic Inputs: stochastic %K and %D length configuration
//@param stoch_k Length for stochastic %K calculation
//@param stoch_d Length for stochastic %D calculation
//@param stoch_source Source input for stochastic calculation
stoch_k = input.int(14, title="Stochastic %K Length", group="PMC Inputs")
stoch_d = input.int(3, title="%D Length", group="PMC Inputs")
stoch_source = source

//@description ROC Inputs: rate of change (ROC) calculation settings
//@param roc_length ROC calculation length
//@param roc_source Source input for ROC calculation
roc_length = input.int(14, title="ROC Length", group="PMC Inputs")
roc_source =source

//@description MACD Inputs: MACD calculation settings
//@param macd_fast_length Fast EMA length for MACD
//@param macd_slow_length Slow EMA length for MACD
//@param macd_signal_length Signal line length for MACD
macd_fast_length = input.int(12, title="MACD Fast Length", group="PMC Inputs")
macd_slow_length = input.int(26, title="MACD Slow Length", group="PMC Inputs")
macd_signal_length = input.int(9, title="MACD Signal Length", group="PMC Inputs")

//@description MFI Inputs: money flow index (MFI) calculation settings
//@param mfi_length MFI length
//@param mfi_source Source input for MFI calculation
mfi_length = input.int(14, title="MFI Length", group="PMC Inputs")
mfi_source = source

//@description CMO Inputs: Chande Momentum Oscillator settings
//@param cmo_length CMO calculation length
cmo_length = input.int(14, title="CMO Length", group="PMC Inputs")

//@description Williams %R Inputs: Williams %R length settings
//@param wr_length Length for Williams %R calculation
wr_length = input.int(14, title="Williams %R Length", group="PMC Inputs")

//@description Ultimate Oscillator Inputs: ultimate oscillator calculation settings
//@param uo_short Short length for ultimate oscillator
//@param uo_medium Medium length for ultimate oscillator
//@param uo_long Long length for ultimate oscillator
uo_short = input.int(7, title="Ultimate Oscillator Short Length", group="PMC Inputs")
uo_medium = input.int(14, title="Ultimate Oscillator Medium Length", group="PMC Inputs")
uo_long = input.int(28, title="Ultimate Oscillator Long Length", group="PMC Inputs")

//@description Detrended Price Oscillator (DPO) Inputs: DPO length settings
//@param dpo_length DPO calculation length
//@param dpo_source Source input for DPO calculation
dpo_length = input.int(20, title="DPO Length", group="PMC Inputs")
dpo_source = source

//@description Relative Price Change Inputs: length for calculating relative price change
//@param length_rpc Relative price change calculation length
length_rpc = input.int(100, title="Relative Change Length", minval=1, tooltip="Length for Relative Change Calculation", group="Relative Changes Settings")

//@description GARCH Volatility Inputs: GARCH length settings
//@param garch_length Length for GARCH calculation
garch_length = input(30, title="GARCH Length", group="GARCH Settings")

//@description Wavelet Transform Inputs: wavelet transform settings
//@param wavelet_length Length for wavelet transform calculation
//@param wavelet_threshold Threshold for detecting significant wavelet signals
//@param hysteresis_buffer Hysteresis buffer to prevent rapid switching between regimes
//@param smoothing_length Length for smoothing the wavelet output
wavelet_length = input(50, title="Wavelet Length", group="Wavelet Settings", tooltip="Length of the window for wavelet transform")
smoothing_length = input(10, title="Smoothing Length", group="Wavelet Settings", tooltip="Length for smoothing the wavelet output")

//@description Mean Reversion Inputs: lookback period settings for mean reversion
//@param lookback_hl Lookback period for mean reversion half-life calculation
lookback_hl = input(100, title="Lookback Period", group="Mean Reversion Settings")

//  <>----------------------------<>
//  |     FUNCTIONS AND CALLS     |
//  <>----------------------------<>

//@description Normalize Function
//@param value The value to be normalized
//@param max_value The maximum value for normalization
//@param min_value The minimum value for normalization
//@return Normalized value between -1 and 1
normalize(value, max_value, min_value) =>
    2 * ((value - min_value) / (max_value - min_value)) - 1

//@description ADX Calculation
//@param len The length period for ADX calculation
//@return Plus and minus directional movements
dirmov(len) =>
    up = ta.change(btcHigh)
    down = -ta.change(btcLow)
    plusDM = na(up) ? na : (up > down and up > 0 ? up : 0)
    minusDM = na(down) ? na : (down > up and down > 0 ? down : 0)
    truerange = ta.rma(ta.tr(true), len)
    plus = fixnan(100 * ta.rma(plusDM, len) / truerange)
    minus = fixnan(100 * ta.rma(minusDM, len) / truerange)
    [plus, minus]

//@description ADX Signal Generation
//@param dilen DI length period
//@param adxlen ADX smoothing period
//@return Generated ADX signal
adx(dilen, adxlen) =>
    [plus, minus] = dirmov(dilen)
    sum = plus + minus
    adx = 100 * ta.rma(math.abs(plus - minus) / (sum == 0 ? 1 : sum), adxlen)
sig = adx(dilen, adxlen)

//@description Dynamic normalization of ADX signal
max_adx = ta.highest(sig, adxlen)
min_adx = ta.lowest(sig, adxlen)
normalized_adx = normalize(sig, max_adx, min_adx)

//@description Linear Regression Calculation for KPSS Test Residuals
//@param src Source data for linear regression
//@param length Length for the regression calculation
//@return Linear regression value
f_linreg(src, length) =>
    linreg_val = ta.linreg(src, length, 0)
    linreg_val

//@description KPSS Test Function
//@param KPSSresiduals Array of residuals to perform KPSS test on
//@param length Length for KPSS test calculation
//@return KPSS test statistic
kpss_test(residuals, length) =>
    partial_sums = 0.0
    partial_sum_squared = 0.0
    for i = 0 to length - 1
        partial_sums += nz(residuals[i])
        partial_sum_squared += partial_sums * partial_sums
    lags = math.floor(math.sqrt(length))
    sum_residuals = 0.0
    for i = 0 to length - 1
        sum_residuals += nz(residuals[i]) * nz(residuals[i])

    for lag = 1 to lags
        weight = 1.0 - lag / (lags + 1.0)
        lag_sum = 0.0
        for i = lag to length - 1
            lag_sum += nz(residuals[i]) * nz(residuals[i - lag])
        sum_residuals += 2 * weight * lag_sum
    long_run_variance = sum_residuals / length
    kpss_stat = partial_sum_squared / (length * length * long_run_variance)
    kpss_stat

// @description Run the KPSS test calculations
linreg_values = f_linreg(source, KPSS_LEN)
KPSSresiduals = source - linreg_values
kpss_stat = kpss_test(KPSSresiduals, KPSS_LEN)
max_kpss = ta.highest(kpss_stat, KPSS_LEN)
min_kpss = ta.lowest(kpss_stat, KPSS_LEN)
normalized_kpss_stat = normalize(kpss_stat, max_kpss, min_kpss)

//@description ADF Test Calculation
//@param a Array containing historical data for ADF test
//@return ADF test result (tau) and critical value for comparison
adftest(adf) =>
    int   nobs = array.size(adf) - 1
    float[]  y = array.new_float(na)
    float[]  x = array.new_float(na)
    float[] x0 = array.new_float(na)
    for i = 0 to nobs - 1
        array.push( y, array.get(adf, i) - array.get(adf, i + 1))             
        array.push( x, array.get(adf, i + 1))                             
        array.push(x0, 1.0)                                         
    float crit = -2.5  // Threshold value for mean reversion (adjust if necessary)
    [array.avg(y), crit]

src_adf = source
float[] adf = array.new_float(na)
for i = 0 to lookback - 1
    array.push(adf, src_adf[i])

[tauADF, crit] = adftest(adf)

//@description Dynamic normalization of ADF test
max_adf = ta.highest(tauADF, lookback)
min_adf = ta.lowest(tauADF, lookback)
normalized_tauADF = normalize(tauADF, max_adf, min_adf)

//@description Phillips-Perron (PP) Test Calculation
//@param src Source data for price or returns
//@param pp_length Length for the PP test window
//@return Phillips-Perron test statistic (pp_z)
pp_test(src, pp_length) =>
    logReturns = math.log(src / src[1])
    meanLogReturns = ta.sma(logReturns, pp_length)
    stdLogReturns = ta.stdev(logReturns, pp_length)
    b = ta.correlation(logReturns, logReturns[1], pp_length) * (stdLogReturns / ta.stdev(logReturns[1], pp_length))
    pp_residuals = logReturns - (meanLogReturns + b * logReturns[1])
    pp_z = ((b - 1) / ta.stdev(pp_residuals, pp_length)) / 10  // Adjusted factor
    pp_z

// Call the function for the source price
pp_statistic = pp_test(source, pp_length)

//@description Dynamic normalization of Phillips-Perron test
max_pp = ta.highest(pp_statistic, pp_length)
min_pp = ta.lowest(pp_statistic, pp_length)
normalized_pp_z = normalize(pp_statistic, max_pp, min_pp)

//@description Hurst Exponent Calculation
hurst_exponent(source, length) =>
    mean = ta.sma(source, length)
    cumulative_dev = array.new_float(length, 0.0)
    for i = 0 to length - 1
        array.set(cumulative_dev, i, source[i] - mean)
    cumulative_sum = 0.0
    Y = array.new_float(length, 0.0)
    for i = 0 to length - 1
        cumulative_sum += array.get(cumulative_dev, i)
        array.set(Y, i, cumulative_sum)
    R = array.max(Y) - array.min(Y)
    S = ta.stdev(source, length)
    result = math.log(R / S) / math.log(length)
    result

//@description Calculate Hurst Exponent
hurst = hurst_exponent(source, hurst_length)

//@description Apply rolling median filter to smooth the Hurst Exponent
smoothed_hurst = ta.median(hurst, hurst_median_length)

//@description Apply normalization to smoothed Hurst Exponent
max_hurst = ta.highest(smoothed_hurst, hurst_length)
min_hurst = ta.lowest(smoothed_hurst, hurst_length)
normalized_hurst = normalize(smoothed_hurst, max_hurst, min_hurst)

//@description Momemtum Indicators
[macdLine, _, _] = ta.macd(source, macd_fast_length, macd_slow_length, macd_signal_length)
williams_r = 100 * (source - ta.highest(btcHigh, wr_length)) / (ta.highest(btcHigh, wr_length) - ta.lowest(btcLow, wr_length))

//@description Ultimate Oscillator Calculation
bp = source - math.min(btcLow, source[1])
tr = math.max(btcHigh - btcLow, math.abs(btcHigh - source[1]), math.abs(btcLow - source[1]))
avg7 = ta.sma(bp, uo_short) * uo_short / ta.sma(tr, uo_short)
avg14 = ta.sma(bp, uo_medium) * uo_medium / ta.sma(tr, uo_medium)
avg28 = ta.sma(bp, uo_long) * uo_long / ta.sma(tr, uo_long)
ultimate_osc = 100 * (4 * avg7 + 2 * avg14 + avg28) / 7

//@description Selecting the momentum indicator
momentum = switch momentumType
    "RSI" => ta.rsi(rsi_source, rsi_length)
    "Stochastic" => ta.stoch(stoch_source, btcHigh, btcLow, stoch_k)
    "ROC" => ta.roc(roc_source, roc_length)
    "MACD" => macdLine
    "MFI" => ta.mfi(source, mfi_length)
    "CMO" => ta.cmo(source, cmo_length)
    "Williams %R" => williams_r
    "Ultimate Oscillator" => ultimate_osc
    "DPO" => source[math.floor(dpo_length / 2) + 1] - ta.sma(dpo_source, dpo_length)

//@description Momemtum/correlation analysis
price_momentum_corr = ta.correlation(source, momentum, corr_length)
max_corr = ta.highest(price_momentum_corr, corr_length)
min_corr = ta.lowest(price_momentum_corr, corr_length)
normalized_corr = normalize(price_momentum_corr, max_corr, min_corr)

//@description Calculate Relative Changes in source Price
relative_change = ta.change(source) / source[1]
smoothed_change = ta.sma(relative_change, length_rpc)
min_val = ta.lowest(smoothed_change, length_rpc)
max_val = ta.highest(smoothed_change, length_rpc)
normalized_rpc = (2 * (smoothed_change - min_val) / (max_val - min_val)) - 1

//@description GARCH (1,1) model
alpha = 0.1
beta = 0.85
mu = ta.sma(source, garch_length)
sum_residuals_garch = 0.0

var float variance = 0.0
for i = 0 to garch_length - 1
    sum_residuals_garch += math.pow(source[i] - mu, 2) 
variance := alpha * math.pow(source - mu, 2) + beta * nz(variance[1], sum_residuals_garch / garch_length)
std_dev = math.sqrt(variance) 

max_volatility = ta.highest(std_dev, garch_length)
min_volatility = ta.lowest(std_dev, garch_length)
normalized_garch = normalize(std_dev, max_volatility, min_volatility)

//@description Haar Wavelet Transform Calculation
haar_wavelet(src, length) =>
    sum = 0.0
    for i = 0 to length - 1
        weight = (i % 2 == 0 ? 1 : -1)  // Haar Wavelet Alternating Pattern
        sum += weight * src[i]
    sum / length
wavelet_result = haar_wavelet(source, wavelet_length)

//@description Smooth & Normalize Wavelet Transform
smoothed_wavelet = ta.sma(wavelet_result, smoothing_length)
max_wavelet = ta.highest(smoothed_wavelet, wavelet_length)
min_wavelet = ta.lowest(smoothed_wavelet, wavelet_length)
normalized_wavelet = normalize(smoothed_wavelet, max_wavelet, min_wavelet)

// @description Half life of Mean Reversion
return_percent(src) =>
    ta.change(src) * 100 / src[1]

// @description Returns, mean and linear regression calculation
returns = return_percent(source)
mean_price = ta.sma(source, lookback_hl)
slope = ta.linreg(source, lookback_hl, 0)

// @description Half-Life Calculation
theta = slope * (ta.stdev(source, lookback_hl) / ta.stdev(source[1], lookback_hl))
half_life = math.log(2) / (theta + 1e-9)  // Prevent division by zero
max_half_life = ta.highest(half_life, lookback_hl)
min_half_life = ta.lowest(half_life, lookback_hl)
normalized_half_life = -normalize(half_life, max_half_life, min_half_life)

//  <>----------------------------<>
//  |         AGGREGATION          |
//  <>----------------------------<>

//@description Aggregate signal calculation for regime detection
aggSig = 0.0
indicator_count = 0

if include_adx
    aggSig += normalized_adx
    indicator_count += 1

if include_kpss
    aggSig += normalized_kpss_stat
    indicator_count += 1

if include_adf
    aggSig += normalized_tauADF
    indicator_count += 1

if include_pp
    aggSig += normalized_pp_z
    indicator_count += 1

if include_hurst
    aggSig += normalized_hurst
    indicator_count += 1

if include_corr
    aggSig += normalized_corr
    indicator_count += 1

if include_rpc
    aggSig += normalized_rpc
    indicator_count += 1

if include_garch
    aggSig += normalized_garch
    indicator_count += 1

if include_wavelet
    aggSig += normalized_wavelet
    indicator_count += 1

if include_halflife
    aggSig += normalized_half_life
    indicator_count += 1

aggSig := indicator_count > 0 ? aggSig / indicator_count : na

smoothingLengthFusion = input.int(14, group = "Style Settings")

smoothedAggSig = smoothingLengthFusion > 1 ? ta.sma(aggSig, smoothingLengthFusion) : aggSig


// Add a toggle input for retaining the previous signal
retain_previous_signal = input.bool(true, title="Retain Previous Signal?", group="Style Settings")

// Initialize variables to store the previous signal states
var float prev_aggSig = na
var bool prev_is_trending = na
var bool prev_is_reverting = na
var bool prev_is_neutral = na
var bool is_trending=na
var bool is_reverting=na
var bool is_neutral=na
// Update the signal conditions based on the toggle
if retain_previous_signal
    if not na(prev_aggSig)
        is_trending := smoothedAggSig > trend_threshold ? true : (prev_is_trending and smoothedAggSig <= trend_threshold and smoothedAggSig >= revert_threshold)
        is_reverting := smoothedAggSig < revert_threshold ? true : (prev_is_reverting and smoothedAggSig <= trend_threshold and smoothedAggSig >= revert_threshold)
        is_neutral := not is_trending and not is_reverting
    else
        is_trending := smoothedAggSig > trend_threshold
        is_reverting := smoothedAggSig < revert_threshold
        is_neutral := not is_trending and not is_reverting
else
    is_trending := smoothedAggSig > trend_threshold
    is_reverting := smoothedAggSig < revert_threshold
    is_neutral := not is_trending and not is_reverting

// Store the current signal values as the previous values for the next iteration
prev_aggSig := smoothedAggSig
prev_is_trending := is_trending
prev_is_reverting := is_reverting
prev_is_neutral := is_neutral

// @description Signal Conditions 
// is_trending = smoothedAggSig > trend_threshold
// is_reverting = smoothedAggSig < revert_threshold
// is_neutral = not is_trending and not is_reverting

// //@description Colour Determination 
bullColor_dynamic = aggSig > nz(aggSig[1]) ? bullColor : color.new(bullColor, 60)
bearColor_dynamic = aggSig < nz(aggSig[1]) ? bearColor : color.new(bearColor, 60)
neutralColor_dynamic = color.new(neutralColor, 60)
plot_color = is_trending ? bullColor_dynamic : (is_reverting ? bearColor_dynamic : neutralColor_dynamic)
bgcolor(is_trending ? color.new(bullColor_dynamic, 80) : (is_reverting ? color.new(bearColor_dynamic, 80) : color.new(neutralColor_dynamic, 80)), force_overlay=true)