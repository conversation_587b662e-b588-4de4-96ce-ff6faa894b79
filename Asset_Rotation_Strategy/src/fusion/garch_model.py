"""
GARCH (Generalized Autoregressive Conditional Heteroskedasticity) Model for Fusion System

Translates PineScript GARCH(1,1) volatility model to Python.
GARCH models volatility clustering and helps identify regime changes based on volatility patterns.
"""

import numpy as np
import pandas as pd
from typing import Optional, Tuple
from .base_indicator import BaseFusionIndicator


class GARCHModel(BaseFusionIndicator):
    """GARCH(1,1) model implementation for regime detection"""
    
    def __init__(self, length: int = 30, alpha: float = 0.1, beta: float = 0.85):
        super().__init__("GARCH")
        self.length = length
        self.alpha = alpha  # ARCH parameter
        self.beta = beta    # GARCH parameter
    
    def _calculate_returns(self, prices: pd.Series) -> pd.Series:
        """
        Calculate returns from price series
        
        Args:
            prices: Price series
            
        Returns:
            Returns series
        """
        returns = prices.pct_change().fillna(0)
        return returns
    
    def _calculate_garch_variance(self, returns: pd.Series) -> pd.Series:
        """
        Calculate GARCH(1,1) conditional variance
        
        Args:
            returns: Returns series
            
        Returns:
            Conditional variance series
        """
        # Initialize variance series
        variance = pd.Series(index=returns.index, dtype=float)
        
        # Calculate unconditional variance for initialization
        unconditional_var = returns.var()
        
        # Calculate rolling mean for residuals calculation
        mu = returns.rolling(window=self.length, min_periods=1).mean()
        
        # Initialize first variance value
        if len(returns) > 0:
            variance.iloc[0] = unconditional_var
        
        # Calculate GARCH variance recursively
        for i in range(1, len(returns)):
            # Get current return and mean
            current_return = returns.iloc[i]
            current_mu = mu.iloc[i]
            
            # Calculate squared residual
            residual_squared = (current_return - current_mu) ** 2
            
            # Previous variance
            prev_variance = variance.iloc[i-1] if not pd.isna(variance.iloc[i-1]) else unconditional_var
            
            # GARCH(1,1) equation: σ²(t) = ω + α*ε²(t-1) + β*σ²(t-1)
            # We use residual_squared as ε²(t-1) and prev_variance as σ²(t-1)
            # ω is implicitly handled by the unconditional variance component
            current_variance = self.alpha * residual_squared + self.beta * prev_variance
            
            # Add small constant to prevent zero variance
            current_variance = max(current_variance, 1e-8)
            
            variance.iloc[i] = current_variance
        
        return variance.fillna(unconditional_var)
    
    def _calculate_volatility(self, variance: pd.Series) -> pd.Series:
        """
        Calculate volatility (standard deviation) from variance
        
        Args:
            variance: Variance series
            
        Returns:
            Volatility series
        """
        return np.sqrt(variance.clip(lower=0))
    
    def calculate(self, data: pd.DataFrame, source_column: str = 'close', 
                 **kwargs) -> pd.Series:
        """
        Calculate normalized GARCH volatility values
        
        Args:
            data: OHLCV data
            source_column: Column to use for calculation (default: 'close')
            **kwargs: Additional parameters
            
        Returns:
            Normalized GARCH volatility values between -1 and 1
        """
        if not self.validate_data(data):
            return pd.Series(dtype=float)
        
        if source_column not in data.columns:
            source_column = 'close'
        
        source = data[source_column]
        
        # Calculate returns
        returns = self._calculate_returns(source)
        
        # Calculate GARCH variance
        variance = self._calculate_garch_variance(returns)
        
        # Calculate volatility
        volatility = self._calculate_volatility(variance)
        
        # Dynamic normalization
        max_volatility = volatility.rolling(window=self.length, min_periods=1).max()
        min_volatility = volatility.rolling(window=self.length, min_periods=1).min()
        
        normalized_garch = self.normalize(volatility, max_volatility, min_volatility)
        
        return normalized_garch.fillna(0)
    
    def get_volatility_regime(self, data: pd.DataFrame, low_vol_percentile: float = 25,
                            high_vol_percentile: float = 75, **kwargs) -> pd.Series:
        """
        Get volatility regime based on GARCH volatility percentiles
        
        Args:
            data: OHLCV data
            low_vol_percentile: Percentile threshold for low volatility
            high_vol_percentile: Percentile threshold for high volatility
            **kwargs: Additional parameters
            
        Returns:
            Series with volatility regime: 1 (high vol), 0 (normal), -1 (low vol)
        """
        source = data['close']
        returns = self._calculate_returns(source)
        variance = self._calculate_garch_variance(returns)
        volatility = self._calculate_volatility(variance)
        
        # Calculate rolling percentiles
        low_threshold = volatility.rolling(window=self.length, min_periods=1).quantile(low_vol_percentile/100)
        high_threshold = volatility.rolling(window=self.length, min_periods=1).quantile(high_vol_percentile/100)
        
        # Classify volatility regimes
        regime = pd.Series(0, index=source.index)  # Default to normal
        regime[volatility > high_threshold] = 1   # High volatility
        regime[volatility < low_threshold] = -1   # Low volatility
        
        return regime
    
    def get_volatility_clustering(self, data: pd.DataFrame, **kwargs) -> pd.Series:
        """
        Detect volatility clustering using GARCH model
        
        Args:
            data: OHLCV data
            **kwargs: Additional parameters
            
        Returns:
            Series indicating volatility clustering strength (0-1 scale)
        """
        source = data['close']
        returns = self._calculate_returns(source)
        variance = self._calculate_garch_variance(returns)
        
        # Calculate persistence of volatility (how much current variance depends on past)
        # This is essentially the β parameter's effectiveness
        persistence = pd.Series(self.beta, index=source.index)
        
        # Adjust persistence based on actual variance patterns
        variance_change = variance.diff().abs()
        avg_variance_change = variance_change.rolling(window=self.length, min_periods=1).mean()
        
        # Higher clustering when variance changes are more predictable (lower relative to mean)
        clustering_strength = 1 - self.safe_divide(variance_change, avg_variance_change, 1)
        clustering_strength = clustering_strength.clip(0, 1)
        
        return clustering_strength.fillna(0.5)
    
    def get_raw_volatility(self, data: pd.DataFrame, **kwargs) -> pd.Series:
        """
        Get raw GARCH volatility values (not normalized)
        
        Args:
            data: OHLCV data
            **kwargs: Additional parameters
            
        Returns:
            Raw volatility series
        """
        source = data['close']
        returns = self._calculate_returns(source)
        variance = self._calculate_garch_variance(returns)
        volatility = self._calculate_volatility(variance)
        
        return volatility.fillna(0)
