"""
Wavelet Transform for Fusion System

Translates PineScript Haar Wavelet Transform to Python.
Uses Haar wavelets to detect regime changes and trend reversals in price data.
"""

import numpy as np
import pandas as pd
from typing import Optional
from .base_indicator import BaseFusionIndicator


class WaveletTransform(BaseFusionIndicator):
    """Haar Wavelet Transform implementation for regime detection"""
    
    def __init__(self, length: int = 50, smoothing_length: int = 10):
        super().__init__("Wavelet")
        self.length = length
        self.smoothing_length = smoothing_length
    
    def _haar_wavelet(self, data: pd.Series, window_length: int) -> float:
        """
        Calculate Haar Wavelet Transform for a single window
        
        Args:
            data: Price data window
            window_length: Length of the window
            
        Returns:
            Haar wavelet coefficient
        """
        if len(data) < window_length:
            return 0.0
        
        # Get the last window_length values
        window_data = data.iloc[-window_length:].values
        
        # Apply Haar wavelet pattern (alternating +1, -1 weights)
        wavelet_sum = 0.0
        for i in range(window_length):
            weight = 1 if i % 2 == 0 else -1  # Alternating pattern
            wavelet_sum += weight * window_data[i]
        
        # Normalize by window length
        return wavelet_sum / window_length
    
    def _rolling_haar_wavelet(self, data: pd.Series) -> pd.Series:
        """
        Calculate rolling Haar Wavelet Transform
        
        Args:
            data: Price series
            
        Returns:
            Rolling wavelet coefficients
        """
        wavelet_results = pd.Series(index=data.index, dtype=float)
        
        for i in range(self.length - 1, len(data)):
            # Get window data
            window_data = data.iloc[max(0, i - self.length + 1):i + 1]
            
            # Calculate Haar wavelet for this window
            wavelet_coeff = self._haar_wavelet(window_data, len(window_data))
            wavelet_results.iloc[i] = wavelet_coeff
        
        return wavelet_results.fillna(0)
    
    def _smooth_wavelet(self, wavelet_series: pd.Series) -> pd.Series:
        """
        Smooth wavelet coefficients using simple moving average
        
        Args:
            wavelet_series: Raw wavelet coefficients
            
        Returns:
            Smoothed wavelet coefficients
        """
        return wavelet_series.rolling(window=self.smoothing_length, min_periods=1).mean()
    
    def calculate(self, data: pd.DataFrame, source_column: str = 'close', 
                 **kwargs) -> pd.Series:
        """
        Calculate normalized Haar Wavelet Transform values
        
        Args:
            data: OHLCV data
            source_column: Column to use for calculation (default: 'close')
            **kwargs: Additional parameters
            
        Returns:
            Normalized wavelet values between -1 and 1
        """
        if not self.validate_data(data):
            return pd.Series(dtype=float)
        
        if source_column not in data.columns:
            source_column = 'close'
        
        source = data[source_column]
        
        # Calculate rolling Haar wavelet
        wavelet_result = self._rolling_haar_wavelet(source)
        
        # Smooth the wavelet coefficients
        smoothed_wavelet = self._smooth_wavelet(wavelet_result)
        
        # Dynamic normalization
        max_wavelet = smoothed_wavelet.rolling(window=self.length, min_periods=1).max()
        min_wavelet = smoothed_wavelet.rolling(window=self.length, min_periods=1).min()
        
        normalized_wavelet = self.normalize(smoothed_wavelet, max_wavelet, min_wavelet)
        
        return normalized_wavelet.fillna(0)
    
    def get_trend_reversals(self, data: pd.DataFrame, threshold: float = 0.5,
                          **kwargs) -> pd.Series:
        """
        Detect trend reversals using wavelet coefficients
        
        Args:
            data: OHLCV data
            threshold: Threshold for significant wavelet signals
            **kwargs: Additional parameters
            
        Returns:
            Series with reversal signals: 1 (upward reversal), -1 (downward reversal), 0 (no reversal)
        """
        source = data['close']
        wavelet_result = self._rolling_haar_wavelet(source)
        smoothed_wavelet = self._smooth_wavelet(wavelet_result)
        
        # Detect reversals based on wavelet coefficient magnitude and sign changes
        reversals = pd.Series(0, index=source.index)
        
        # Strong positive wavelet coefficient suggests upward reversal
        reversals[smoothed_wavelet > threshold] = 1
        
        # Strong negative wavelet coefficient suggests downward reversal
        reversals[smoothed_wavelet < -threshold] = -1
        
        return reversals
    
    def get_regime_transitions(self, data: pd.DataFrame, transition_threshold: float = 0.3,
                             **kwargs) -> pd.Series:
        """
        Detect regime transitions using wavelet analysis
        
        Args:
            data: OHLCV data
            transition_threshold: Threshold for regime transition detection
            **kwargs: Additional parameters
            
        Returns:
            Series indicating regime transitions (0-1 scale, higher = more likely transition)
        """
        source = data['close']
        wavelet_result = self._rolling_haar_wavelet(source)
        smoothed_wavelet = self._smooth_wavelet(wavelet_result)
        
        # Calculate the rate of change in wavelet coefficients
        wavelet_change = smoothed_wavelet.diff().abs()
        
        # Normalize the change rate
        max_change = wavelet_change.rolling(window=self.length, min_periods=1).max()
        normalized_change = self.safe_divide(wavelet_change, max_change, 0)
        
        # Regime transitions are likely when wavelet coefficients change rapidly
        transition_probability = normalized_change.clip(0, 1)
        
        return transition_probability.fillna(0)
    
    def get_frequency_components(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        Decompose price data into different frequency components using wavelets
        
        Args:
            data: OHLCV data
            **kwargs: Additional parameters
            
        Returns:
            DataFrame with different frequency components
        """
        source = data['close']
        
        # Calculate wavelets at different scales
        components = pd.DataFrame(index=source.index)
        
        # Short-term component (high frequency)
        short_wavelet = self._rolling_haar_wavelet(source)
        components['high_freq'] = self._smooth_wavelet(short_wavelet)
        
        # Medium-term component
        medium_length = self.length * 2
        medium_wavelet = pd.Series(index=source.index, dtype=float)
        for i in range(medium_length - 1, len(source)):
            window_data = source.iloc[max(0, i - medium_length + 1):i + 1]
            medium_wavelet.iloc[i] = self._haar_wavelet(window_data, len(window_data))
        components['medium_freq'] = self._smooth_wavelet(medium_wavelet.fillna(0))
        
        # Long-term component (low frequency)
        long_length = self.length * 4
        long_wavelet = pd.Series(index=source.index, dtype=float)
        for i in range(long_length - 1, len(source)):
            window_data = source.iloc[max(0, i - long_length + 1):i + 1]
            long_wavelet.iloc[i] = self._haar_wavelet(window_data, len(window_data))
        components['low_freq'] = self._smooth_wavelet(long_wavelet.fillna(0))
        
        return components.fillna(0)
    
    def get_raw_wavelet(self, data: pd.DataFrame, **kwargs) -> pd.Series:
        """
        Get raw wavelet coefficients (not normalized or smoothed)
        
        Args:
            data: OHLCV data
            **kwargs: Additional parameters
            
        Returns:
            Raw wavelet coefficient series
        """
        source = data['close']
        return self._rolling_haar_wavelet(source)
