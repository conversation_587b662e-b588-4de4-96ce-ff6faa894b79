"""
Fusion Aggregator - Main class that combines all 10 regime detection indicators

This class aggregates signals from all fusion indicators to provide a unified
regime detection signal for filtering false breakouts and improving trading strategies.
"""

import numpy as np
import pandas as pd
from typing import Dict, Optional, Any, Tuple
from dataclasses import dataclass

from .adx_indicator import ADXIndicator
from .kpss_test import KPSSTest
from .adf_test import ADFTest
from .pp_test import PPTest
from .hurst_exponent import HurstExponent
from .pmc_correlation import PMCCorrelation
from .rpc_calculation import RPCCalculation
from .garch_model import GARCHModel
from .wavelet_transform import WaveletTransform
from .halflife_calculation import HalfLifeCalculation


@dataclass
class FusionConfig:
    """Configuration for fusion indicators"""
    # Indicator inclusion flags
    include_adx: bool = False
    include_kpss: bool = False
    include_adf: bool = True
    include_pp: bool = True
    include_hurst: bool = True
    include_corr: bool = True
    include_rpc: bool = True
    include_garch: bool = True
    include_wavelet: bool = True
    include_halflife: bool = False
    
    # Thresholds
    trend_threshold: float = 0.1
    revert_threshold: float = -0.1
    
    # Smoothing
    smoothing_length: int = 14
    retain_previous_signal: bool = True
    
    # Indicator parameters
    adx_di_length: int = 28
    adx_length: int = 20
    kpss_length: int = 85
    adf_lookback: int = 110
    pp_length: int = 100
    hurst_length: int = 25
    hurst_median_length: int = 25
    correlation_length: int = 200
    momentum_type: str = "MACD"
    rpc_length: int = 100
    garch_length: int = 30
    wavelet_length: int = 50
    wavelet_smoothing: int = 10
    halflife_lookback: int = 100


class FusionAggregator:
    """Main fusion aggregator class"""
    
    def __init__(self, config: Optional[FusionConfig] = None):
        self.config = config or FusionConfig()
        
        # Initialize indicators
        self.indicators = {
            'adx': ADXIndicator(self.config.adx_di_length, self.config.adx_length),
            'kpss': KPSSTest(self.config.kpss_length),
            'adf': ADFTest(self.config.adf_lookback),
            'pp': PPTest(self.config.pp_length),
            'hurst': HurstExponent(self.config.hurst_length, self.config.hurst_median_length),
            'pmc': PMCCorrelation(self.config.correlation_length, self.config.momentum_type),
            'rpc': RPCCalculation(self.config.rpc_length),
            'garch': GARCHModel(self.config.garch_length),
            'wavelet': WaveletTransform(self.config.wavelet_length, self.config.wavelet_smoothing),
            'halflife': HalfLifeCalculation(self.config.halflife_lookback)
        }
        
        # State variables for signal retention
        self._prev_signals = {}
        
    def calculate_individual_signals(self, data: pd.DataFrame, **kwargs) -> Dict[str, pd.Series]:
        """
        Calculate all individual indicator signals
        
        Args:
            data: OHLCV data
            **kwargs: Additional parameters
            
        Returns:
            Dictionary of indicator signals
        """
        signals = {}
        
        if self.config.include_adx:
            signals['adx'] = self.indicators['adx'].calculate(data, **kwargs)
            
        if self.config.include_kpss:
            signals['kpss'] = self.indicators['kpss'].calculate(data, **kwargs)
            
        if self.config.include_adf:
            signals['adf'] = self.indicators['adf'].calculate(data, **kwargs)
            
        if self.config.include_pp:
            signals['pp'] = self.indicators['pp'].calculate(data, **kwargs)
            
        if self.config.include_hurst:
            signals['hurst'] = self.indicators['hurst'].calculate(data, **kwargs)
            
        if self.config.include_corr:
            signals['pmc'] = self.indicators['pmc'].calculate(data, **kwargs)
            
        if self.config.include_rpc:
            signals['rpc'] = self.indicators['rpc'].calculate(data, **kwargs)
            
        if self.config.include_garch:
            signals['garch'] = self.indicators['garch'].calculate(data, **kwargs)
            
        if self.config.include_wavelet:
            signals['wavelet'] = self.indicators['wavelet'].calculate(data, **kwargs)
            
        if self.config.include_halflife:
            signals['halflife'] = self.indicators['halflife'].calculate(data, **kwargs)
        
        return signals
    
    def aggregate_signals(self, signals: Dict[str, pd.Series]) -> pd.Series:
        """
        Aggregate individual signals into unified signal
        
        Args:
            signals: Dictionary of individual indicator signals
            
        Returns:
            Aggregated signal series
        """
        if not signals:
            return pd.Series(dtype=float)
        
        # Get common index
        common_index = None
        for signal in signals.values():
            if common_index is None:
                common_index = signal.index
            else:
                common_index = common_index.intersection(signal.index)
        
        if common_index.empty:
            return pd.Series(dtype=float)
        
        # Sum all signals
        agg_signal = pd.Series(0.0, index=common_index)
        indicator_count = 0
        
        for signal in signals.values():
            agg_signal += signal.reindex(common_index, fill_value=0)
            indicator_count += 1
        
        # Average the signals
        if indicator_count > 0:
            agg_signal = agg_signal / indicator_count
        
        return agg_signal
    
    def smooth_signal(self, signal: pd.Series) -> pd.Series:
        """
        Apply smoothing to aggregated signal
        
        Args:
            signal: Raw aggregated signal
            
        Returns:
            Smoothed signal
        """
        if self.config.smoothing_length > 1:
            return signal.rolling(window=self.config.smoothing_length, min_periods=1).mean()
        return signal

    def detect_regime(self, smoothed_signal: pd.Series) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """
        Detect market regime based on smoothed aggregated signal

        Args:
            smoothed_signal: Smoothed aggregated signal

        Returns:
            Tuple of (is_trending, is_reverting, is_neutral) boolean series
        """
        # Initialize regime series
        is_trending = pd.Series(False, index=smoothed_signal.index)
        is_reverting = pd.Series(False, index=smoothed_signal.index)
        is_neutral = pd.Series(False, index=smoothed_signal.index)

        if self.config.retain_previous_signal:
            # Implement hysteresis logic to retain previous signals
            prev_trending = False
            prev_reverting = False
            prev_neutral = False

            for i, (idx, value) in enumerate(smoothed_signal.items()):
                if pd.isna(value):
                    # Use previous state if current value is NaN
                    is_trending.loc[idx] = prev_trending
                    is_reverting.loc[idx] = prev_reverting
                    is_neutral.loc[idx] = prev_neutral
                else:
                    # Determine new state with hysteresis
                    if value > self.config.trend_threshold:
                        is_trending.loc[idx] = True
                        is_reverting.loc[idx] = False
                        is_neutral.loc[idx] = False
                    elif value < self.config.revert_threshold:
                        is_trending.loc[idx] = False
                        is_reverting.loc[idx] = True
                        is_neutral.loc[idx] = False
                    else:
                        # In neutral zone - retain previous state if it was trending or reverting
                        if prev_trending and value >= self.config.revert_threshold:
                            is_trending.loc[idx] = True
                            is_reverting.loc[idx] = False
                            is_neutral.loc[idx] = False
                        elif prev_reverting and value <= self.config.trend_threshold:
                            is_trending.loc[idx] = False
                            is_reverting.loc[idx] = True
                            is_neutral.loc[idx] = False
                        else:
                            is_trending.loc[idx] = False
                            is_reverting.loc[idx] = False
                            is_neutral.loc[idx] = True

                # Update previous state
                prev_trending = is_trending.loc[idx]
                prev_reverting = is_reverting.loc[idx]
                prev_neutral = is_neutral.loc[idx]
        else:
            # Simple threshold-based regime detection
            is_trending = smoothed_signal > self.config.trend_threshold
            is_reverting = smoothed_signal < self.config.revert_threshold
            is_neutral = ~(is_trending | is_reverting)

        return is_trending, is_reverting, is_neutral

    def calculate(self, data: pd.DataFrame, **kwargs) -> Dict[str, Any]:
        """
        Main calculation method that returns complete fusion analysis

        Args:
            data: OHLCV data
            **kwargs: Additional parameters

        Returns:
            Dictionary containing all fusion results
        """
        # Calculate individual signals
        individual_signals = self.calculate_individual_signals(data, **kwargs)

        # Aggregate signals
        agg_signal = self.aggregate_signals(individual_signals)

        # Smooth aggregated signal
        smoothed_signal = self.smooth_signal(agg_signal)

        # Detect regimes
        is_trending, is_reverting, is_neutral = self.detect_regime(smoothed_signal)

        # Create regime signal (-1: reverting, 0: neutral, 1: trending)
        regime_signal = pd.Series(0, index=smoothed_signal.index)
        regime_signal[is_trending] = 1
        regime_signal[is_reverting] = -1

        return {
            'individual_signals': individual_signals,
            'aggregated_signal': agg_signal,
            'smoothed_signal': smoothed_signal,
            'is_trending': is_trending,
            'is_reverting': is_reverting,
            'is_neutral': is_neutral,
            'regime_signal': regime_signal,
            'regime_strength': np.abs(smoothed_signal)
        }

    def get_regime_signal(self, data: pd.DataFrame, **kwargs) -> pd.Series:
        """
        Get simple regime signal for integration with other systems

        Args:
            data: OHLCV data
            **kwargs: Additional parameters

        Returns:
            Regime signal series (-1: reverting, 0: neutral, 1: trending)
        """
        results = self.calculate(data, **kwargs)
        return results['regime_signal']

    def get_regime_confidence(self, data: pd.DataFrame, **kwargs) -> pd.Series:
        """
        Get confidence level of regime detection

        Args:
            data: OHLCV data
            **kwargs: Additional parameters

        Returns:
            Confidence series (0-1 scale)
        """
        results = self.calculate(data, **kwargs)

        # Confidence is based on how far the signal is from neutral zone
        smoothed_signal = results['smoothed_signal']

        # Calculate distance from neutral zone
        trend_distance = np.maximum(0, smoothed_signal - self.config.trend_threshold)
        revert_distance = np.maximum(0, self.config.revert_threshold - smoothed_signal)

        # Combine distances
        confidence = np.maximum(trend_distance, revert_distance)

        # Normalize to 0-1 scale
        max_confidence = confidence.rolling(window=50, min_periods=1).max()
        normalized_confidence = confidence / (max_confidence + 1e-8)

        return normalized_confidence.fillna(0).clip(0, 1)
