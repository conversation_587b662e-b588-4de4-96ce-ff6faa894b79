#!/usr/bin/env python
"""
Individual Component Visual Tests with Hysteresis Logic

This script tests fusion components individually with TradingView-style visualizations
and includes the "retain previous signal" hysteresis logic to avoid neutral zones.
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import argparse
from datetime import datetime, timedelta
from typing import Dict, Any, Tuple

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from data_fetcher import fetch_ohlcv_data
from fusion.adx_indicator import ADXIndicator
from fusion.kpss_test import KPSSTest
from fusion.adf_test import ADFTest


def fetch_btc_data(start_date: str = None, end_date: str = None, period: str = "1y") -> pd.DataFrame:
    """Fetch BTC data using existing data fetcher"""
    try:
        if end_date:
            end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        else:
            end_dt = datetime.now()
            
        if start_date:
            start_dt = datetime.strptime(start_date, "%Y-%m-%d")
            print(f"Fetching BTC data from {start_date} to {end_dt.strftime('%Y-%m-%d')}")
        else:
            days = 365 if period == "1y" else 730 if period == "2y" else 180
            start_dt = end_dt - timedelta(days=days)
            print(f"Fetching BTC data for period: {period}")
        
        date_diff = (end_dt - start_dt).days
        limit = min(date_diff + 10, 1000)
        
        data_dict = fetch_ohlcv_data(
            exchange_id='binance',
            symbols=['BTC/USDT'],
            timeframe='1d',
            since=start_dt,
            limit=limit,
            use_cache=True
        )
        
        if 'BTC/USDT' not in data_dict or data_dict['BTC/USDT'].empty:
            raise ValueError("No BTC/USDT data retrieved")
        
        data = data_dict['BTC/USDT']
        print(f"Retrieved {len(data)} data points from {data.index[0]} to {data.index[-1]}")
        return data
        
    except Exception as e:
        print(f"Error fetching data: {e}")
        print("Creating synthetic test data...")
        
        days = 365 if period == "1y" else 730 if period == "2y" else 180
        dates = pd.date_range(start=datetime.now() - timedelta(days=days), 
                             end=datetime.now(), freq='D')
        np.random.seed(42)
        
        initial_price = 45000
        returns = np.random.normal(0.001, 0.03, len(dates))
        prices = [initial_price]
        
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        data = pd.DataFrame({
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
            'close': prices,
            'volume': np.random.uniform(1000, 10000, len(dates))
        }, index=dates)
        
        data['high'] = data[['open', 'close', 'high']].max(axis=1)
        data['low'] = data[['open', 'close', 'low']].min(axis=1)
        
        print(f"Synthetic test data: {len(data)} periods")
        return data


def apply_hysteresis_logic(signal: pd.Series, trend_threshold: float = 0.1, 
                          revert_threshold: float = -0.1, 
                          retain_previous: bool = True) -> Tuple[pd.Series, pd.Series, pd.Series]:
    """
    Apply hysteresis logic to avoid neutral zones (matching PineScript logic)
    
    Args:
        signal: Input signal series
        trend_threshold: Threshold for trending regime
        revert_threshold: Threshold for reverting regime  
        retain_previous: Whether to retain previous signal in neutral zone
        
    Returns:
        Tuple of (is_trending, is_reverting, is_neutral) boolean series
    """
    is_trending = pd.Series(False, index=signal.index)
    is_reverting = pd.Series(False, index=signal.index)
    is_neutral = pd.Series(False, index=signal.index)
    
    if retain_previous:
        # Initialize previous states
        prev_trending = False
        prev_reverting = False
        
        for i, (idx, value) in enumerate(signal.items()):
            if pd.isna(value):
                # Use previous state if current value is NaN
                is_trending.loc[idx] = prev_trending
                is_reverting.loc[idx] = prev_reverting
                is_neutral.loc[idx] = not (prev_trending or prev_reverting)
            else:
                # Apply hysteresis logic (matching PineScript)
                if value > trend_threshold:
                    # Clear trending signal
                    is_trending.loc[idx] = True
                    is_reverting.loc[idx] = False
                    is_neutral.loc[idx] = False
                elif value < revert_threshold:
                    # Clear reverting signal
                    is_trending.loc[idx] = False
                    is_reverting.loc[idx] = True
                    is_neutral.loc[idx] = False
                else:
                    # In neutral zone - retain previous state if it was trending or reverting
                    if prev_trending and value >= revert_threshold:
                        # Retain trending if signal hasn't crossed revert threshold
                        is_trending.loc[idx] = True
                        is_reverting.loc[idx] = False
                        is_neutral.loc[idx] = False
                    elif prev_reverting and value <= trend_threshold:
                        # Retain reverting if signal hasn't crossed trend threshold
                        is_trending.loc[idx] = False
                        is_reverting.loc[idx] = True
                        is_neutral.loc[idx] = False
                    else:
                        # Default to neutral
                        is_trending.loc[idx] = False
                        is_reverting.loc[idx] = False
                        is_neutral.loc[idx] = True
            
            # Update previous states
            prev_trending = is_trending.loc[idx]
            prev_reverting = is_reverting.loc[idx]
    else:
        # Simple threshold-based regime detection (no hysteresis)
        is_trending = signal > trend_threshold
        is_reverting = signal < revert_threshold
        is_neutral = ~(is_trending | is_reverting)
    
    return is_trending, is_reverting, is_neutral


def plot_component_tradingview_style(data: pd.DataFrame, signal: pd.Series, 
                                   component_name: str, trend_threshold: float = 0.1,
                                   revert_threshold: float = -0.1,
                                   smoothing_length: int = 1,
                                   retain_previous: bool = True,
                                   figsize: Tuple[int, int] = (16, 12)) -> plt.Figure:
    """Create TradingView-style plot for individual component with hysteresis"""
    
    # Apply smoothing to signal if requested
    if smoothing_length > 1:
        smoothed_signal = signal.rolling(window=smoothing_length, min_periods=1).mean()
        signal_label = f'{component_name} Signal (Smoothed {smoothing_length})'
    else:
        smoothed_signal = signal
        signal_label = f'{component_name} Signal (Raw)'
    
    # Apply hysteresis logic
    is_trending, is_reverting, is_neutral = apply_hysteresis_logic(
        smoothed_signal, trend_threshold, revert_threshold, retain_previous
    )
    
    # Create figure with dark background
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=figsize, sharex=True, 
                                   gridspec_kw={'height_ratios': [3, 1]})
    fig.patch.set_facecolor('#1e1e1e')
    
    # Plot 1: Price with regime background
    ax1.set_facecolor('#1e1e1e')
    
    # Add regime background colors
    add_regime_background(ax1, smoothed_signal.index, is_trending, is_reverting, is_neutral)
    
    # Plot price line
    ax1.plot(data.index, data['close'], color='#00d4aa', linewidth=1.5, alpha=0.9, label='BTC/USDT')
    
    # Add high/low shadows (sampled)
    sample_indices = range(0, len(data), max(1, len(data)//200))
    for i in sample_indices:
        ax1.plot([data.index[i], data.index[i]], 
                [data['low'].iloc[i], data['high'].iloc[i]], 
                color='#00d4aa', linewidth=0.5, alpha=0.6)
    
    # Style the price plot
    style_tradingview_axis(ax1)
    hysteresis_text = "with Hysteresis" if retain_previous else "without Hysteresis"
    ax1.set_title(f'BTC/USDT - {component_name} Regime Detection ({hysteresis_text})', 
                  color='white', fontsize=16, fontweight='bold', pad=20)
    ax1.set_ylabel('Price (USDT)', color='white', fontsize=12)
    
    # Add regime legend
    add_regime_legend(ax1)
    
    # Plot 2: Component signal
    ax2.set_facecolor('#1e1e1e')
    
    # Plot raw and smoothed signals
    if smoothing_length > 1:
        ax2.plot(signal.index, signal, color='#666666', linewidth=1, alpha=0.6, label=f'{component_name} Raw')
        ax2.plot(smoothed_signal.index, smoothed_signal, color='#ffffff', linewidth=2, alpha=0.9, label=signal_label)
    else:
        ax2.plot(signal.index, signal, color='#ffffff', linewidth=2, alpha=0.9, label=signal_label)
    
    # Add threshold lines
    ax2.axhline(y=0, color='#666666', linestyle='-', alpha=0.5, linewidth=1)
    ax2.axhline(y=trend_threshold, color='#1f4e79', linestyle='--', alpha=0.8, linewidth=1, label='Trend Threshold')
    ax2.axhline(y=revert_threshold, color='#7d1f1f', linestyle='--', alpha=0.8, linewidth=1, label='Revert Threshold')
    
    # Style the signal plot
    style_tradingview_axis(ax2)
    ax2.set_ylabel(f'{component_name}\nSignal', color='white', fontsize=12)
    ax2.set_xlabel('Date', color='white', fontsize=12)
    ax2.legend(loc='upper right', facecolor='#2a2a2a', edgecolor='white', labelcolor='white')
    
    # Format dates
    ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
    ax2.xaxis.set_major_locator(mdates.MonthLocator(interval=2))
    plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45, color='white')
    
    plt.tight_layout()
    return fig


def add_regime_background(ax: plt.Axes, index: pd.Index, is_trending: pd.Series,
                         is_reverting: pd.Series, is_neutral: pd.Series):
    """Add regime background colors"""
    trending_color = '#1f4e79'    # Blue for trending
    reverting_color = '#7d1f1f'   # Red for mean reverting
    neutral_color = '#2a2a2a'     # Dark gray for neutral

    current_regime = None
    start_idx = None

    for i in range(len(is_trending)):
        idx = index[i]

        if is_trending.iloc[i]:
            regime = 'trending'
            color = trending_color
        elif is_reverting.iloc[i]:
            regime = 'reverting'
            color = reverting_color
        else:
            regime = 'neutral'
            color = neutral_color

        if current_regime != regime:
            if current_regime is not None and start_idx is not None:
                ax.axvspan(start_idx, idx, alpha=0.3, color=prev_color, zorder=0)

            current_regime = regime
            start_idx = idx
            prev_color = color

    # Close the last regime background
    if current_regime is not None and start_idx is not None:
        ax.axvspan(start_idx, index[-1], alpha=0.3, color=prev_color, zorder=0)


def style_tradingview_axis(ax: plt.Axes):
    """Apply TradingView styling to axis"""
    ax.grid(True, color='#2a2a2a', linestyle='-', linewidth=0.5, alpha=0.7)
    ax.set_facecolor('#1e1e1e')
    ax.tick_params(colors='white', which='both')
    ax.xaxis.label.set_color('white')
    ax.yaxis.label.set_color('white')

    for spine in ax.spines.values():
        spine.set_color('#2a2a2a')


def add_regime_legend(ax: plt.Axes):
    """Add regime legend"""
    from matplotlib.patches import Patch
    legend_elements = [
        Patch(facecolor='#1f4e79', alpha=0.3, label='Trending Regime'),
        Patch(facecolor='#7d1f1f', alpha=0.3, label='Mean Reverting Regime'),
        Patch(facecolor='#2a2a2a', alpha=0.3, label='Neutral Regime')
    ]
    ax.legend(handles=legend_elements, loc='upper left', facecolor='#2a2a2a',
             edgecolor='white', labelcolor='white')


def test_component_with_visualization(component, component_name: str, data: pd.DataFrame,
                                    output_dir: str, smoothing_length: int = 1,
                                    retain_previous: bool = True, **kwargs) -> Dict[str, Any]:
    """Test individual component and create visualization with hysteresis"""
    print(f"\n{'='*60}")
    print(f"TESTING {component_name.upper()} COMPONENT")
    print(f"{'='*60}")

    # Calculate component signal
    print(f"Calculating {component_name} signal...")
    raw_signal = component.calculate(data, **kwargs)

    # Apply smoothing if requested
    if smoothing_length > 1:
        signal = raw_signal.rolling(window=smoothing_length, min_periods=1).mean()
        print(f"Applied smoothing with length {smoothing_length}")
    else:
        signal = raw_signal

    # Print statistics
    print(f"\n{component_name.upper()} STATISTICS:")
    print(f"- Valid values: {signal.notna().sum()}/{len(signal)}")
    print(f"- Mean: {signal.mean():.4f}")
    print(f"- Std: {signal.std():.4f}")
    print(f"- Min: {signal.min():.4f}")
    print(f"- Max: {signal.max():.4f}")

    if smoothing_length > 1:
        print(f"\nRAW SIGNAL STATISTICS (before smoothing):")
        print(f"- Mean: {raw_signal.mean():.4f}")
        print(f"- Std: {raw_signal.std():.4f}")
        print(f"- Min: {raw_signal.min():.4f}")
        print(f"- Max: {raw_signal.max():.4f}")

    # Apply hysteresis logic for regime classification
    trend_threshold = 0.1
    revert_threshold = -0.1

    is_trending, is_reverting, is_neutral = apply_hysteresis_logic(
        signal, trend_threshold, revert_threshold, retain_previous
    )

    trending_count = is_trending.sum()
    reverting_count = is_reverting.sum()
    neutral_count = is_neutral.sum()
    total = len(signal)

    hysteresis_text = "with Hysteresis" if retain_previous else "without Hysteresis"
    print(f"\nREGIME CLASSIFICATION ({hysteresis_text}):")
    print(f"- Trending: {trending_count} periods ({trending_count/total*100:.1f}%)")
    print(f"- Mean Reverting: {reverting_count} periods ({reverting_count/total*100:.1f}%)")
    print(f"- Neutral: {neutral_count} periods ({neutral_count/total*100:.1f}%)")

    # Create visualization
    print(f"Creating {component_name} visualization...")
    fig = plot_component_tradingview_style(data, raw_signal, component_name,
                                         trend_threshold, revert_threshold,
                                         smoothing_length, retain_previous)

    # Save plot
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    hysteresis_suffix = "_hysteresis" if retain_previous else "_no_hysteresis"
    filename = os.path.join(output_dir, f"{component_name.lower()}_component{hysteresis_suffix}_{timestamp}.png")
    fig.savefig(filename, dpi=300, bbox_inches='tight', facecolor='#1e1e1e', edgecolor='none')
    plt.close(fig)

    print(f"Plot saved: {filename}")

    # Show recent values
    print(f"\nRECENT VALUES (last 10 periods):")
    recent_data = pd.DataFrame({
        'date': signal.index[-10:],
        'close_price': data['close'].reindex(signal.index[-10:]),
        'signal': signal.iloc[-10:],
        'regime': ['T' if is_trending.iloc[i] else 'R' if is_reverting.iloc[i] else 'N'
                  for i in range(-10, 0)]
    })
    print(recent_data.to_string(index=False, float_format='%.4f'))

    return {
        'signal': signal,
        'statistics': {
            'mean': signal.mean(),
            'std': signal.std(),
            'min': signal.min(),
            'max': signal.max()
        },
        'regime_counts': {
            'trending': trending_count,
            'reverting': reverting_count,
            'neutral': neutral_count
        },
        'filename': filename
    }


def run_individual_component_tests(start_date: str = None, end_date: str = None,
                                  period: str = "1y", smoothing_length: int = 1,
                                  retain_previous: bool = True):
    """Run visual tests for the first three components with hysteresis"""
    print("=" * 80)
    print("INDIVIDUAL FUSION COMPONENTS VISUAL TESTING")
    print("=" * 80)

    # Fetch BTC data
    data = fetch_btc_data(start_date=start_date, end_date=end_date, period=period)

    # Create output directory
    output_dir = "individual_components_output"
    os.makedirs(output_dir, exist_ok=True)

    # Initialize components with PineScript-matching parameters
    components = {
        'ADX': {
            'component': ADXIndicator(di_length=28, adx_length=20),
            'kwargs': {}
        },
        'KPSS': {
            'component': KPSSTest(length=85),
            'kwargs': {}
        },
        'ADF': {
            'component': ADFTest(lookback=110),
            'kwargs': {}
        }
    }

    results = {}

    # Test each component
    for name, config in components.items():
        try:
            result = test_component_with_visualization(
                config['component'],
                name,
                data,
                output_dir,
                smoothing_length=smoothing_length,
                retain_previous=retain_previous,
                **config['kwargs']
            )
            results[name] = result

        except Exception as e:
            print(f"\n❌ ERROR testing {name}: {str(e)}")
            import traceback
            traceback.print_exc()
            results[name] = None

    # Summary
    print(f"\n" + "=" * 80)
    print("INDIVIDUAL COMPONENTS TEST SUMMARY")
    print("=" * 80)

    hysteresis_text = "with Hysteresis" if retain_previous else "without Hysteresis"
    print(f"Configuration: Smoothing={smoothing_length}, {hysteresis_text}")

    successful_tests = 0
    for name, result in results.items():
        if result is not None:
            print(f"✅ {name}: Successfully tested and visualized")
            print(f"   - Signal range: [{result['statistics']['min']:.4f}, {result['statistics']['max']:.4f}]")
            print(f"   - Trending: {result['regime_counts']['trending']} periods")
            print(f"   - Reverting: {result['regime_counts']['reverting']} periods")
            print(f"   - Neutral: {result['regime_counts']['neutral']} periods")
            print(f"   - Plot: {result['filename']}")
            successful_tests += 1
        else:
            print(f"❌ {name}: Test failed")

    print(f"\nCompleted: {successful_tests}/{len(components)} components tested successfully")

    if successful_tests == len(components):
        print(f"\n🎉 All individual component tests completed successfully!")
        print(f"Check the '{output_dir}' directory for TradingView-style visualizations.")
        print(f"\nCompare these plots with TradingView to identify discrepancies.")
        print(f"The hysteresis logic should reduce neutral zones and match PineScript behavior.")
    else:
        print(f"\n⚠️  Some component tests failed. Check the error messages above.")

    return results


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Test individual fusion components with TradingView-style visualizations and hysteresis logic",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Test with default settings (1 year, no smoothing, with hysteresis)
  python test_individual_components_visual.py

  # Test with specific date range
  python test_individual_components_visual.py --start-date 2023-01-01 --end-date 2024-01-01

  # Test with smoothing applied to signals
  python test_individual_components_visual.py --smoothing-length 7

  # Test without hysteresis logic (simple thresholds)
  python test_individual_components_visual.py --no-hysteresis

  # Test with custom date range, smoothing, and no hysteresis
  python test_individual_components_visual.py --start-date 2023-01-01 --smoothing-length 14 --no-hysteresis
        """
    )

    parser.add_argument(
        '--start-date',
        type=str,
        help='Start date in YYYY-MM-DD format (e.g., 2023-01-01)'
    )

    parser.add_argument(
        '--end-date',
        type=str,
        help='End date in YYYY-MM-DD format (defaults to today)'
    )

    parser.add_argument(
        '--period',
        type=str,
        default='1y',
        choices=['6mo', '1y', '2y'],
        help='Period to fetch if start-date not specified (default: 1y)'
    )

    parser.add_argument(
        '--smoothing-length',
        type=int,
        default=1,
        help='Smoothing length for individual signals (1 = no smoothing, default: 1)'
    )

    parser.add_argument(
        '--no-hysteresis',
        action='store_true',
        help='Disable hysteresis logic (use simple thresholds instead)'
    )

    return parser.parse_args()


if __name__ == "__main__":
    # Parse command line arguments
    args = parse_arguments()

    print("Starting individual fusion components visual testing...")
    print("This will test ADX, KPSS, and ADF components with TradingView-style visualizations.")

    if args.start_date:
        print(f"Using custom date range: {args.start_date} to {args.end_date or 'today'}")
    else:
        print(f"Using period: {args.period}")

    print(f"Smoothing length: {args.smoothing_length}")
    print(f"Hysteresis logic: {'Disabled' if args.no_hysteresis else 'Enabled'}")

    # Validate date format if provided
    if args.start_date:
        try:
            datetime.strptime(args.start_date, "%Y-%m-%d")
        except ValueError:
            print("Error: start-date must be in YYYY-MM-DD format")
            sys.exit(1)

    if args.end_date:
        try:
            datetime.strptime(args.end_date, "%Y-%m-%d")
        except ValueError:
            print("Error: end-date must be in YYYY-MM-DD format")
            sys.exit(1)

    # Run tests
    results = run_individual_component_tests(
        start_date=args.start_date,
        end_date=args.end_date,
        period=args.period,
        smoothing_length=args.smoothing_length,
        retain_previous=not args.no_hysteresis
    )

    print(f"\nTesting completed at {datetime.now()}")
    print("Check the 'individual_components_output' directory for generated plots.")
    print("Compare the plots with TradingView output to identify any remaining discrepancies.")
